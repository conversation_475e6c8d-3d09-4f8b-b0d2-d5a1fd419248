const sqlite3 = require('sqlite3').verbose();
const path = require('path');

async function showFileIndexes() {
    const dbPath = path.join(__dirname, '../full_index.db');
    const db = new sqlite3.Database(dbPath);

    try {
        // 查询文件信息
        const filePath = '终端安全\\天擎_faq.txt.003';
        const fileInfo = await new Promise((resolve, reject) => {
            db.get(
                'SELECT id, file_path, file_name FROM documents WHERE file_path = ?',
                [filePath],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });

        if (!fileInfo) {
            console.log(`文件 ${filePath} 未在索引中找到`);
            return;
        }

        console.log(`文件信息: ${fileInfo.file_path}`);
        console.log('----------------------------------------');

        // 查询该文件的所有索引词
        const words = await new Promise((resolve, reject) => {
            const words = [];
            db.each(
                `SELECT word, line_number, position 
                 FROM word_index 
                 WHERE document_id = ? 
                 ORDER BY line_number, position`,
                [fileInfo.id],
                (err, row) => {
                    if (err) reject(err);
                    else words.push(row);
                },
                (err) => {
                    if (err) reject(err);
                    else resolve(words);
                }
            );
        });

        console.log(`找到 ${words.length} 个索引词:`);
        words.forEach((word, index) => {
            console.log(`${index + 1}. 词: "${word.word}" - 行: ${word.line_number}, 位置: ${word.position}`);
        });

    } catch (error) {
        console.error('查询出错:', error);
    } finally {
        db.close();
    }
}

showFileIndexes();
