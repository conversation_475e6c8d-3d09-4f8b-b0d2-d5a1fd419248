<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>奇安信 95015 智能助手</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="app">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <button class="new-chat-btn" id="newChatBtn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 5v14M5 12h14"></path>
                    </svg>
                    新对话
                </button>
            </div>
            <div class="chat-history" id="chatHistory">
                <!-- 对话历史将在这里显示 -->
            </div>
        </div>

        <!-- 主聊天区域 -->
        <div class="main-chat">
            <!-- 头部 -->
            <div class="chat-header">
                <div class="header-title">
                    <h1>奇安信 95015 智能助手</h1>
                    <span class="status-indicator">在线</span>
                </div>
                <div class="header-actions">
                    <button class="action-btn" id="clearBtn" title="清空对话">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 6h18M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6m3 0V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- 消息区域 -->
            <div class="messages-container" id="messagesContainer">
                <div class="messages" id="messages">
                    <!-- 欢迎消息 -->
                    <div class="message-group assistant">
                        <div class="message-avatar">
                            <div class="avatar-circle">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="message-content">
                            <div class="message-text">
                                您好！我是奇安信 95015 智能助手，有什么可以帮助您的吗？
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="input-container">
                <div class="input-wrapper">
                    <div class="input-box">
                        <textarea
                            id="messageInput"
                            placeholder="发送消息..."
                            rows="1"
                            maxlength="4000"
                        ></textarea>
                        <button class="send-button" id="sendBtn" disabled>
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z"/>
                            </svg>
                        </button>
                    </div>
                    <div class="input-footer">
                        <span class="input-hint">按 Enter 发送消息，Shift+Enter 换行</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script_fixed.js"></script>
</body>
</html>
