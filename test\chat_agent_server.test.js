const request = require('supertest');
const sinon = require('sinon');
const { expect } = require('chai');
const app = require('../chat_agent_server');
const { callModelApi, executeTool } = require('../chat_agent_server').__test__;

describe('Chat Agent Server API', function() {
  this.timeout(10000); // 设置测试超时时间为10秒
  describe('重试机制测试', () => {
    afterEach(() => {
      sinon.restore();
    });

    it('API调用失败时应重试3次', async function() {
      this.timeout(10000); // 设置测试超时时间为10秒
      // 使用dynamic import来获取node-fetch
      const fetchModule = await import('node-fetch');
      const fetchStub = sinon.stub().rejects(new Error('API error'));
      sinon.stub(fetchModule, 'default').value(fetchStub);

      try {
        await callModelApi([{role: 'user', content: 'test'}]);
        expect.fail('Should have thrown an error');
      } catch (error) {
        // 等待所有重试完成，实际等待3秒（3次重试，每次1秒）
        await new Promise(resolve => setTimeout(resolve, 3500));
        expect(fetchStub.callCount).to.equal(3);
        expect(error.message).to.include('API请求失败');
      }
    });

    it('工具执行失败时应重试3次', async function() {
      this.timeout(10000); // 设置测试超时时间为10秒
      const toolInfo = { name: 'test_tool' };
      
      // 创建一个总是失败的工具函数
      const failingTool = sinon.stub().rejects(new Error('Tool error'));
      
      // 保存原始的executeTool函数
      const originalExecuteTool = app.__test__.executeTool;
      
      // 临时替换executeTool函数
      app.__test__.executeTool = async (toolInfo, retryCount = 0) => {
        return failingTool(toolInfo);
      };

      try {
        await app.__test__.executeTool(toolInfo);
        expect.fail('Should have thrown an error');
      } catch (error) {
        // 等待所有重试完成，实际等待3秒（3次重试，每次1秒）
        await new Promise(resolve => setTimeout(resolve, 3500));
        expect(failingTool.callCount).to.equal(3);
        expect(error.message).to.include('Tool error');
      } finally {
        // 恢复原始实现
        app.__test__.executeTool = originalExecuteTool;
      }
    });

    it('API调用重试成功后应返回响应', async function() {
      this.timeout(10000); // 设置测试超时时间为10秒
      // 使用dynamic import来获取node-fetch
      const fetchModule = await import('node-fetch');
      const fetchStub = sinon.stub()
        .onFirstCall().rejects(new Error('API error'))
        .onSecondCall().resolves({ ok: true });
      
      sinon.stub(fetchModule, 'default').value(fetchStub);

      const response = await callModelApi([{role: 'user', content: 'test'}]);
      // 等待重试完成，实际等待1秒（1次重试，每次1秒）
      await new Promise(resolve => setTimeout(resolve, 1500));
      expect(fetchStub.callCount).to.equal(2);
      expect(response.ok).to.be.true;
    });
  });

  describe('基础功能测试', () => {
    it('健康检查应返回200', async () => {
      const response = await request(app).get('/api/health');
      expect(response.status).to.equal(200);
      expect(response.body.status).to.equal('ok');
    });
  });
});
