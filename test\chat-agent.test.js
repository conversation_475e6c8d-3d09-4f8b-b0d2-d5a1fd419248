const { parseToolCall, parseSearchFilesInfo, getDocumentPathContents, manageContext, estimateTokens, listFiles, readFile, searchFiles, searchDocuments, executeMcpTool } = require('../chat-agent.js');

// 动态导入chai
let expect;
before(async function() {
    const chai = await import('chai');
    expect = chai.expect;
});

describe('Chat Agent Tests', function() {
    describe('parseToolCall Function', function() {
        it('should parse 回答问题 tool call correctly', function() {
            const toolContent = `
<回答问题>
<answer>根据您的问题，解决方案是在设备设置中找到网络配置选项，然后选择VPN连接并输入您的凭证。</answer>
<reference>
    <path>/docs/vpn_setup_guide.txt</path>
    <name>VPN设置指南</name>
</reference>
</回答问题>
            `;
            
            const result = parseToolCall(toolContent);
            expect(result).to.be.an('object');
            expect(result.name).to.equal('回答问题');
            expect(result.paramValue).to.be.an('object');
            expect(result.paramValue.answer).to.be.a('string');
            expect(result.paramValue.references).to.be.an('array');
        });
        
        it('should parse search_files tool call correctly', function() {
            const toolContent = `
<search_files>
<path>documents/边界安全</path>
<regex>VPN</regex>
<file_pattern>*.txt</file_pattern>
</search_files>
            `;
            
            const result = parseToolCall(toolContent);
            expect(result).to.be.an('object');
            expect(result.name).to.equal('search_files');
            expect(result.paramValue).to.be.null;
        });
        
        it('should parse read_file tool with directory parameter correctly', function() {
            const toolContent = `
<read_file>
<path>test</path>
</read_file>
            `;
            
            const result = parseToolCall(toolContent);
            expect(result).to.be.an('object');
            expect(result.name).to.equal('read_file');
            expect(result.paramValue).to.be.an('object');
            expect(result.paramValue.path).to.equal('test');
        });
        
        it('should parse read_file tool with file and line range correctly', function() {
            const toolContent = `
<read_file>
<path>test/chat-agent.test.js</path>
<line>1-10</line>
</read_file>
            `;
            
            const result = parseToolCall(toolContent);
            expect(result).to.be.an('object');
            expect(result.name).to.equal('read_file');
            expect(result.paramValue).to.be.an('object');
            expect(result.paramValue.path).to.equal('test/chat-agent.test.js');
            expect(result.paramValue.line).to.equal('1-10');
        });
        
        it('should return null for invalid tool call', function() {
            const toolContent = `
<无效工具>
<参数>测试参数</参数>
</无效工具>
            `;
            
            const result = parseToolCall(toolContent);
            expect(result).to.be.null;
        });
    });
    
    describe('parseSearchFilesInfo Function', function() {
        it('should parse search_files info with file pattern correctly', function() {
            const toolContent = `
<search_files>
<path>documents/边界安全</path>
<regex>VPN</regex>
<file_pattern>*.txt</file_pattern>
</search_files>
            `;
            
            const result = parseSearchFilesInfo(toolContent);
            expect(result).to.be.an('object');
            expect(result.path).to.equal('documents/边界安全');
            expect(result.regex).to.equal('VPN');
            expect(result.filePattern).to.equal('*.txt');
        });
        
        it('should parse search_files info without file pattern correctly', function() {
            const toolContent = `
<search_files>
<path>documents/天擎</path>
<regex>天擎</regex>
</search_files>
            `;
            
            const result = parseSearchFilesInfo(toolContent);
            expect(result).to.be.an('object');
            expect(result.path).to.equal('documents/天擎');
            expect(result.regex).to.equal('天擎');
            expect(result.filePattern).to.be.null;
        });
    });
    
    describe('getDocumentPathContents Function', function() {
        it('should return environment detail string', function() {
            const result = getDocumentPathContents();
            expect(result).to.be.a('string');
            expect(result).to.include('<environment_detail>');
            expect(result).to.include('DocumentPath:');
        });
        
        it('should include product description from file', function() {
            const result = getDocumentPathContents();
            expect(result).to.be.a('string');
            // 检查是否包含产品简介标题
            expect(result).to.include('# Product Description');
            // 检查是否包含info.txt文件中的产品简介内容
            expect(result).to.include('类目：天擎');
        });
    });
    
    describe('Tool Parsing', function() {
        it('should successfully parse valid tool call', function() {
            const toolContent = `
<回答问题>
<answer>根据您的问题，解决方案是在设备设置中找到网络配置选项，然后选择VPN连接并输入您的凭证。</answer>
</回答问题>
            `;
            
            const toolInfo = parseToolCall(toolContent);
            expect(toolInfo).to.be.an('object');
            expect(toolInfo.name).to.equal('回答问题');
        });
        
        it('should return null for invalid tool call', function() {
            const toolContent = `
<无效工具>
<参数>测试参数</参数>
</无效工具>
            `;
            
            const toolInfo = parseToolCall(toolContent);
            expect(toolInfo).to.be.null;
        });
    });
    
    describe('manageContext Function', function() {
        this.timeout(120000); // 增加超时时间到120秒
        
        it('should return messages directly if token count is within limit', async function() {
            const messages = [
                { role: 'system', content: 'System message' },
                { role: 'user', content: 'User message' }
            ];
            const maxTokens = 1000;  // 设置一个较大的限制
            
            const result = await manageContext(messages, maxTokens);
            expect(result).to.deep.equal(messages);
        });
        
        it('should remove older assistant/tool messages if token count exceeds limit', async function() {
            // 创建一些长消息以超出限制
            const longContent = 'A'.repeat(100);  // 创建一个较短的长内容
            const messages = [
                { role: 'system', content: 'System message' },
                { role: 'assistant', content: longContent },
                { role: 'tool', content: longContent },
                { role: 'user', content: 'Recent user message' }
            ];
            const maxTokens = 50;  // 设置一个更小的限制
            
            const result = await manageContext(messages, maxTokens);
            
            // 检查是否去除了部分历史消息
            expect(result.length).to.be.lessThan(messages.length);
            // 检查是否保留了最新的用户消息
            expect(result[result.length - 1].role).to.equal('user');
        });
        
        it('should filter tool result based on relevance if still exceeding limit after removing history', async function() {
            // 创建一些长消息以超出限制
            const longContent = 'A'.repeat(1000);  // 创建一个较长的内容
            const messages = [
                { role: 'system', content: 'System message' },
                { role: 'tool', content: longContent },
                { role: 'user', content: 'Recent user message' }
            ];
            const maxTokens = 200;  // 设置一个适中的限制
            
            const result = await manageContext(messages, maxTokens);
            
            // 检查tool result是否被处理（可能被过滤或截断）
            expect(result[1].content).to.satisfy((content) => {
                return content.includes('[内容已根据相关性筛选]') || content.includes('[内容已截断]');
            });
        });
        
        it('should handle search_files tool result with file boundary chunking', async function() {
            // 创建一个模拟的search_files结果，包含多个文件信息
            let searchFilesResult = `在目录"test"中找到以下匹配项:\n`;
            for (let i = 0; i < 5; i++) {
                searchFilesResult += `文件: test/file${i}.txt:1-100\n`;
                for (let j = 1; j <= 100; j++) {
                    searchFilesResult += `${j}: 这是文件${i}的第${j}行，内容很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长\n`;
                }
                searchFilesResult += '\n';
            }
            
            const messages = [
                { role: 'system', content: 'System message' },
                { role: 'tool', content: `[search_files] result: ${searchFilesResult}` },
                { role: 'user', content: 'Recent user message' }
            ];
            const maxTokens = 500;  // 设置一个很小的限制，确保触发chunk处理
            
            const result = await manageContext(messages, maxTokens);
            
            // 检查tool result是否被处理
            expect(result[1].content).to.satisfy((content) => {
                return content.includes('[内容已根据相关性筛选]') || content.includes('[内容已截断]') || content.includes('[文件列表]');
            });
        });
        
        it('should handle read_file tool result with newline chunking and overlap', async function() {
            // 创建一个模拟的read_file结果，包含多行内容
            let readFileResult = `文件"test/sample.txt"的内容:\n`;
            for (let i = 1; i <= 5; i++) {
                readFileResult += `这是第${i}行，内容很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长\n`;
            }
            
            const messages = [
                { role: 'system', content: 'System message' },
                { role: 'tool', content: `[read_file] result: ${readFileResult}` },
                { role: 'user', content: 'Recent user message' }
            ];
            const maxTokens = 30;  // 设置一个很小的限制，确保触发chunk处理
            
            const result = await manageContext(messages, maxTokens);
            
            // 检查tool result是否被处理
            expect(result[1].content).to.satisfy((content) => {
                return content.includes('[内容已根据相关性筛选]') || content.includes('[内容已截断]');
            });
        });
        
        it('should handle other tool types with original chunking strategy', async function() {
            // 创建一个模拟的其他工具类型结果
            const otherToolResult = `这是一个很长的结果内容，`.repeat(100);
            
            const messages = [
                { role: 'system', content: 'System message' },
                { role: 'tool', content: `[other_tool] result: ${otherToolResult}` },
                { role: 'user', content: 'Recent user message' }
            ];
            const maxTokens = 100;  // 设置一个小的限制
            
            const result = await manageContext(messages, maxTokens);
            
            // 检查tool result是否被处理
            expect(result[1].content).to.satisfy((content) => {
                return content.includes('[内容已根据相关性筛选]') || content.includes('[内容已截断]');
            });
        });
    });
    
    describe('estimateTokens Function', function() {
        it('should estimate token count based on character count', function() {
            const messages = [
                { role: 'user', content: 'This is a test message with 39 characters.' }
            ];
            const estimatedTokens = estimateTokens(messages);
            // 39个字符除以4约等于10个token
            expect(estimatedTokens).to.be.closeTo(10, 5);  // 允许一定的误差
        });
        
        it('should handle empty messages', function() {
            const messages = [
                { role: 'user', content: '' }
            ];
            const estimatedTokens = estimateTokens(messages);
            expect(estimatedTokens).to.equal(0);
        });
        
        it('should handle null content', function() {
            const messages = [
                { role: 'user', content: null }
            ];
            const estimatedTokens = estimateTokens(messages);
            expect(estimatedTokens).to.equal(0);
        });
    });
    
    describe('listFiles Function', function() {
        it('should list files in a directory', function() {
            const result = listFiles('.');
            expect(result).to.be.a('string');
            expect(result).to.include('目录');
        });
        
        it('should handle non-existent directory', function() {
            const result = listFiles('./non-existent-directory');
            expect(result).to.be.a('string');
            expect(result).to.include('不存在');
        });
        
        it('should handle directory parameter correctly', function() {
            const result = listFiles('test');
            expect(result).to.be.a('string');
            expect(result).to.include('test');
        });
        
        it('should handle recursive listing', function() {
            const result = listFiles({ path: 'test', recursive: true });
            expect(result).to.be.a('string');
            expect(result).to.include('test');
        });
        
        it('should handle empty directory', function() {
            // 创建一个临时空目录进行测试
            const fs = require('fs');
            const tempDir = './temp-empty-dir';
            if (!fs.existsSync(tempDir)) {
                fs.mkdirSync(tempDir);
            }
            
            const result = listFiles(tempDir);
            expect(result).to.be.a('string');
            expect(result).to.include('为空');
            
            // 清理临时目录
            fs.rmdirSync(tempDir);
        });
    });
    
    describe('readFile Function', function() {
        it('should read file content', function() {
            // 由于工作目录已切换到documentPath，需要使用相对于项目根目录的路径
            // 先切换回项目根目录以访问测试文件
            const originalCwd = process.cwd();
            process.chdir('..');
            const result = readFile('test/chat-agent.test.js');
            process.chdir(originalCwd); // 恢复工作目录
            expect(result).to.be.a('string');
            expect(result).to.include('describe');
        });
        
        it('should handle non-existent file', function() {
            const result = readFile('non-existent-file.txt');
            expect(result).to.be.a('string');
            expect(result).to.include('不存在');
        });
        
        it('should handle directory as parameter', function() {
            // 由于工作目录已切换到documentPath，需要使用相对于项目根目录的路径
            // 先切换回项目根目录以访问测试目录
            const originalCwd = process.cwd();
            process.chdir('..');
            const result = readFile('test');
            process.chdir(originalCwd); // 恢复工作目录
            expect(result).to.be.a('string');
            expect(result).to.include('test');
        });
        
        it('should handle directory parameter and return directory listing', function() {
            // 由于工作目录已切换到documentPath，需要使用相对于项目根目录的路径
            // 先切换回项目根目录以访问测试目录
            const originalCwd = process.cwd();
            process.chdir('..');
            const result = readFile('test');
            process.chdir(originalCwd); // 恢复工作目录
            expect(result).to.be.a('string');
            // 检查是否包含目录名称
            expect(result).to.include('test');
            // 检查是否包含目录内容信息
            expect(result).to.satisfy((str) => {
                return str.includes('不存在') || str.includes('内容') || str.includes('目录');
            });
        });
        
        it('should handle read_file tool call with directory parameter', function() {
            // 由于工作目录已切换到documentPath，需要使用相对于项目根目录的路径
            // 先切换回项目根目录以访问测试目录
            const originalCwd = process.cwd();
            process.chdir('..');
            
            // 模拟工具调用返回read_file但参数是目录的情况
            const toolContent = `
<read_file>
<path>test</path>
</read_file>
            `;
            
            // 解析工具调用
            const toolInfo = parseToolCall(toolContent);
            expect(toolInfo).to.be.an('object');
            expect(toolInfo.name).to.equal('read_file');
            expect(toolInfo.paramValue).to.be.an('object');
            expect(toolInfo.paramValue.path).to.equal('test');
            
            // 执行工具
            const result = readFile(toolInfo.paramValue.path);
            process.chdir(originalCwd); // 恢复工作目录
            expect(result).to.be.a('string');
            // 检查结果是否包含目录名称
            expect(result).to.include('test');
            // 检查结果是否包含适当的错误信息或目录内容信息
            expect(result).to.satisfy((str) => {
                return str.includes('不存在') || str.includes('内容') || str.includes('目录');
            });
        });
        
        it('should read file with line range', function() {
            // 由于工作目录已切换到documentPath，需要使用相对于项目根目录的路径
            // 先切换回项目根目录以访问测试文件
            const originalCwd = process.cwd();
            process.chdir('..');
            const result = readFile('test/chat-agent.test.js', '1-5');
            process.chdir(originalCwd); // 恢复工作目录
            expect(result).to.be.a('string');
            expect(result).to.include('1:');
            expect(result).to.include('5:');
        });
        
        it('should handle invalid line range', function() {
            // 由于工作目录已切换到documentPath，需要使用相对于项目根目录的路径
            // 先切换回项目根目录以访问测试文件
            const originalCwd = process.cwd();
            process.chdir('..');
            const result = readFile('test/chat-agent.test.js', 'invalid-range');
            process.chdir(originalCwd); // 恢复工作目录
            expect(result).to.be.a('string');
            expect(result).to.include('test/chat-agent.test.js');
        });
    });
    
    describe('searchFiles Function', function() {
        it('should search files with valid parameters', function() {
            const toolContent = `
<search_files>
<path>test</path>
<regex>describe</regex>
<file_pattern>*.js</file_pattern>
</search_files>
            `;
            
            const result = searchFiles(toolContent);
            expect(result).to.be.a('string');
            expect(result).to.include('test');
        });
        
        it('should handle missing path parameter', function() {
            const toolContent = `
<search_files>
<regex>test</regex>
</search_files>
            `;
            
            const result = searchFiles(toolContent);
            expect(result).to.be.a('string');
            expect(result).to.include('错误');
        });
        
        it('should handle missing regex parameter', function() {
            const toolContent = `
<search_files>
<path>test</path>
</search_files>
            `;
            
            const result = searchFiles(toolContent);
            expect(result).to.be.a('string');
            expect(result).to.include('错误');
        });
        
        it('should handle non-existent directory', function() {
            const toolContent = `
<search_files>
<path>non-existent-dir</path>
<regex>test</regex>
</search_files>
            `;
            
            const result = searchFiles(toolContent);
            expect(result).to.be.a('string');
            expect(result).to.include('不存在');
        });
        
        it('should handle invalid regex', function() {
            const toolContent = `
<search_files>
<path>test</path>
<regex>[invalid</regex>
</search_files>
            `;
            
            const result = searchFiles(toolContent);
            expect(result).to.be.a('string');
            expect(result).to.include('无效的正则表达式');
        });
    });
    
    describe('searchDocuments Function', function() {
        it('should search documents with keywords', function() {
            const keywords = ['test'];
            const result = searchDocuments(keywords);
            expect(result).to.be.a('string');
        });
        
        it('should handle empty keywords', function() {
            const keywords = [];
            const result = searchDocuments(keywords);
            expect(result).to.be.a('string');
        });
        
        it('should handle single keyword', function() {
            const keywords = 'test';
            const result = searchDocuments(keywords);
            expect(result).to.be.a('string');
        });
        
        it('should handle non-existent directories', function() {
            // 测试当documents和knowledge目录都不存在时的行为
            const keywords = ['test'];
            const result = searchDocuments(keywords);
            expect(result).to.be.a('string');
        });
    });
    
    describe('executeMcpTool Function', function() {
        it('should return error message when MCP server is not connected', async function() {
            const toolInfo = {
                name: 'test-server:test-tool',
                tag: 'test-tool',
                paramValue: null,
                fullContent: '<test-tool></test-tool>',
                isMcpTool: true,
                serverName: 'test-server'
            };
            
            const result = await executeMcpTool(toolInfo);
            expect(result).to.be.a('string');
            expect(result).to.include('错误：MCP服务器"test-server"未连接');
        });
    });
});
