// indexer.js
const fs = require('fs');
const zlib = require('zlib');
// 暂时不使用外部分词库，使用改进的简单分词

/**
 * 轻量中文全文检索（无外部数据库）
 * 特性：
 * - 倒排索引 + 词位置
 * - 最小覆盖窗口做距离主排序
 * - OR 检索：覆盖率惩罚（命中词/查询词）^2
 * - 多行 snippet，命中行距 < mergeGap 自动合并
 * - 可持久化到文件
 */
class CNFullText {
  constructor(options = {}) {
    this.options = {
      textField: options.textField || 'text', // 文本字段
      idField: options.idField || 'id',       // 文档唯一ID字段
      stopwords: new Set(options.stopwords || []),
      // 片段展示参数（可在 search 里传入覆盖）
      snippetWidth: options.snippetWidth ?? 40,
      mergeGap: options.mergeGap ?? 10,
    };

    this.docs = new Map();     // id -> 原始文档（text 已做normalize）
    // term -> { df, postings: Array<{ id, positions:number[] }> }
    this.inverted = new Map();
    // term -> idf
    this.idf = new Map();

    // 使用改进的简单分词，不依赖外部库
  }

  // —— 基础工具 —— //
  _normalize(str) {
    // 保留换行符和空格，只替换制表符等其他空白字符
    return (str || '').replace(/[\t\f\v]+/g, ' ').trim(); // 保留空格和换行符作为分词边界
  }

  _tokenize(text) {
    // 使用jieba-js分词；我们用“分词序号”当作位置用于窗口/距离计算
    let segs;
    try {
      // 尝试使用 jieba-js 进行分词
      if (this.useJieba && jieba) {
        // jieba-js的cut方法返回数组
        segs = jieba.cut(text);
        // 检查返回值是否有效
        if (!segs || !Array.isArray(segs)) {
          console.warn('jieba分词返回无效结果，使用简单分词');
          segs = this._simpleTokenize(text);
        }
      } else {
        // 如果 jieba 不可用，使用简单的字符级分割
        segs = this._simpleTokenize(text);
      }
    } catch (error) {
      console.warn('jieba分词失败，使用简单分词:', error.message);
      segs = this._simpleTokenize(text);
    }

    // 确保segs是数组
    if (!Array.isArray(segs)) {
      segs = this._simpleTokenize(text);
    }

    const tokens = [];
    for (const t0 of segs) {
      const t = (t0 || '').trim();
      if (!t) continue;
      if (this.options.stopwords.has(t)) continue;
      tokens.push(t);
    }
    return tokens;
  }

  _simpleTokenize(text) {
    // 简化的bi-gram分词：只生成双字词
    const segments = [];

    // 更全面的标点符号和分隔符（包括空格）
    const punctuation = /[，。！？；：""''（）【】《》、\s\:\-\=\+\|\\\/_]+/;
    const parts = text.split(punctuation).filter(p => p.trim());

    for (const part of parts) {
      if (!part.trim()) continue;

      // 检查是否包含英文
      const hasEnglish = /[a-zA-Z]/.test(part);
      const hasChinese = /[\u4e00-\u9fff]/.test(part);

      if (hasEnglish && !hasChinese) {
        // 纯英文词，直接添加
        segments.push(part);
      } else if (!hasEnglish && hasChinese) {
        // 纯中文，使用bi-gram分词
        if (part.length === 1) {
          // 单字直接保留
          segments.push(part);
        } else {
          // 生成所有双字词（bi-gram）
          for (let i = 0; i < part.length - 1; i++) {
            segments.push(part.slice(i, i + 2));
          }
        }
      } else if (hasEnglish && hasChinese) {
        // 中英文混合，尝试分离
        const mixedParts = part.split(/([a-zA-Z]+)/);
        for (const mixedPart of mixedParts) {
          if (mixedPart.trim()) {
            if (/^[a-zA-Z]+$/.test(mixedPart)) {
              segments.push(mixedPart);
            } else {
              // 中文部分使用bi-gram处理
              const chineseParts = this._tokenizeChinese(mixedPart);
              segments.push(...chineseParts);
            }
          }
        }
      } else {
        // 其他情况，直接添加
        segments.push(part);
      }
    }

    return segments.filter(seg => seg && seg.trim());
  }

  _tokenizeChinese(text) {
    // 专门处理中文文本的bi-gram分词
    const segments = [];
    const punctuation = /[，。！？；：""''（）【】《》、\:\-\=\+\|\\\/_]+/;
    const parts = text.split(punctuation).filter(p => p.trim());

    for (const part of parts) {
      if (!part.trim()) continue;

      if (part.length === 1) {
        // 单字直接保留
        segments.push(part);
      } else {
        // 生成所有双字词（bi-gram）
        for (let i = 0; i < part.length - 1; i++) {
          segments.push(part.slice(i, i + 2));
        }
      }
    }

    return segments.filter(seg => seg && seg.trim());
  }

  // —— 建索引（一次性） —— //
  build(docsArray) {
    this.docs.clear();
    this.inverted.clear();
    this.idf.clear();

    for (const raw of docsArray) {
      const id = raw[this.options.idField];
      const text = this._normalize(raw[this.options.textField]);
      if (id == null || typeof text !== 'string') continue;

      const tokens = this._tokenize(text);
      this.docs.set(id, { ...raw, [this.options.textField]: text });

      const positionsByTerm = new Map();
      tokens.forEach((term, idx) => {
        if (!positionsByTerm.has(term)) positionsByTerm.set(term, []);
        positionsByTerm.get(term).push(idx);
      });

      positionsByTerm.forEach((positions, term) => {
        if (!this.inverted.has(term)) {
          this.inverted.set(term, { df: 0, postings: [] });
        }
        const entry = this.inverted.get(term);
        entry.df += 1;
        entry.postings.push({ id, positions });
      });
    }

    // 计算 idf
    const N = this.docs.size || 1;
    for (const [term, entry] of this.inverted.entries()) {
      // 加平滑，避免 0
      const idf = Math.log((N - entry.df + 0.5) / (entry.df + 0.5) + 1);
      this.idf.set(term, idf);
    }
    return this;
  }

  // —— 持久化（压缩二进制格式）—— //
  save(filePath) {
    const payload = {
      options: {
        textField: this.options.textField,
        idField: this.options.idField,
        stopwords: [...this.options.stopwords],
        snippetWidth: this.options.snippetWidth,
        mergeGap: this.options.mergeGap,
      },
      docs: [...this.docs.values()],
      inverted: [...this.inverted.entries()],
      idf: [...this.idf.entries()],
    };

    try {
      // 序列化为JSON字符串
      const jsonStr = JSON.stringify(payload);
      const jsonBuffer = Buffer.from(jsonStr, 'utf8');

      // 使用gzip压缩
      const compressed = zlib.gzipSync(jsonBuffer, { level: 6 }); // 平衡压缩率和速度

      // 构建文件头
      const header = Buffer.from('CNFT', 'ascii'); // CNFullText标识
      const version = Buffer.alloc(4);
      version.writeUInt32LE(2, 0); // 版本号2（支持gzip压缩）
      const originalLength = Buffer.alloc(4);
      originalLength.writeUInt32LE(jsonBuffer.length, 0); // 原始长度
      const compressedLength = Buffer.alloc(4);
      compressedLength.writeUInt32LE(compressed.length, 0); // 压缩后长度

      // 组合最终文件
      const finalBuffer = Buffer.concat([header, version, originalLength, compressedLength, compressed]);
      fs.writeFileSync(filePath, finalBuffer);

    } catch (error) {
      throw new Error(`保存索引文件失败: ${error.message}`);
    }
  }

  static load(filePath) {
    try {
      const buffer = fs.readFileSync(filePath);

      // 检查魔数
      const header = buffer.subarray(0, 4).toString('ascii');
      if (header !== 'CNFT') {
        // 尝试作为旧的JSON格式加载
        const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        return CNFullText._createFromData(data);
      }

      // 读取版本号
      const version = buffer.readUInt32LE(4);

      let jsonStr;
      if (version === 1) {
        // 版本1：无压缩
        const originalLength = buffer.readUInt32LE(8);
        const compressedData = buffer.subarray(12);
        jsonStr = compressedData.toString('utf8');

        if (jsonStr.length !== originalLength) {
          console.warn(`长度不匹配: 期望 ${originalLength}, 实际 ${jsonStr.length}`);
        }
      } else if (version === 2) {
        // 版本2：gzip压缩
        const originalLength = buffer.readUInt32LE(8);
        const compressedLength = buffer.readUInt32LE(12);
        const compressedData = buffer.subarray(16);

        if (compressedData.length !== compressedLength) {
          throw new Error(`压缩数据长度不匹配: 期望 ${compressedLength}, 实际 ${compressedData.length}`);
        }

        // 解压数据
        const decompressed = zlib.gunzipSync(compressedData);
        const decompressedBuffer = decompressed; // 保持为Buffer
        jsonStr = decompressedBuffer.toString('utf8');

        // 检查长度是否匹配（应该比较字节长度而不是字符串长度）
        if (decompressedBuffer.length !== originalLength) {
          console.warn(`解压后长度不匹配: 期望 ${originalLength}, 实际 ${decompressedBuffer.length}`);
        }
      } else {
        throw new Error(`不支持的版本号: ${version}`);
      }

      const data = JSON.parse(jsonStr);
      return CNFullText._createFromData(data);

    } catch (error) {
      throw new Error(`加载索引文件失败: ${error.message}`);
    }
  }

  // 从数据创建引擎实例的辅助方法
  static _createFromData(data) {
    const engine = new CNFullText({
      textField: data.options.textField,
      idField: data.options.idField,
      stopwords: data.options.stopwords,
      snippetWidth: data.options.snippetWidth,
      mergeGap: data.options.mergeGap,
    });
    data.docs.forEach(doc => engine.docs.set(doc[engine.options.idField], doc));
    engine.inverted = new Map(data.inverted);
    engine.idf = new Map(data.idf);
    return engine;
  }

  // —— 多行 snippet：命中行聚类并高亮（无命中则返回 []） —— //
  _collectSnippets(text, qTokens, width = 40, mergeGap = 10, maxDistance = null) {
    const lines = text.split(/\r?\n/);
    const hitLines = [];

    // 收集命中行（1-based）
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      if (qTokens.some(q => q && line.includes(q))) {
        hitLines.push(i + 1);
      }
    }

    if (hitLines.length === 0) return []; // 按你的要求：没命中不返回预览

    // 合并相近命中行：行距 < mergeGap
    const groups = [];
    let curr = [hitLines[0]];
    for (let i = 1; i < hitLines.length; i++) {
      const h = hitLines[i];
      const prev = curr[curr.length - 1];
      if (h - prev < mergeGap) curr.push(h);
      else { groups.push(curr); curr = [h]; }
    }
    groups.push(curr);

    // 组装片段，并在片段级别应用距离过滤
    const segments = [];
    for (const group of groups) {
      const start = group[0];
      const end = group[group.length - 1];

      // 如果设置了距离限制，检查这个片段中的关键词距离
      if (maxDistance !== null && qTokens.length >= 2) {
        // 提取片段文本进行距离检查
        const snippetText = lines.slice(start - 1, end).join('\n');
        const snippetTokens = this._tokenize(this._normalize(snippetText));

        // 计算片段中查询词的位置
        const snippetTermPositions = [];
        for (const qToken of qTokens) {
          const positions = [];
          snippetTokens.forEach((token, idx) => {
            if (token === qToken) {
              positions.push(idx);
            }
          });
          if (positions.length > 0) {
            snippetTermPositions.push(positions);
          }
        }

        // 如果片段中包含多个查询词，检查距离
        if (snippetTermPositions.length >= 2) {
          const snippetMinWindow = minimalWindowSpan(snippetTermPositions);
          if (snippetMinWindow > maxDistance) {
            continue; // 跳过距离超过限制的片段
          }
        }
      }

      const pieceLines = [];
      for (let ln = start; ln <= end; ln++) {
        let s = lines[ln - 1] ?? '';
        // 高亮所有查询词
        for (const q of qTokens) {
          if (!q) continue;
          s = s.split(q).join(`【${q}】`);
        }
        // 针对超长行做局部截断（围绕首次高亮）
        if (s.length > width * 2) {
          const firstHitIdx = s.indexOf('【');
          const startIdx = firstHitIdx >= 0 ? Math.max(0, firstHitIdx - width) : 0;
          const endIdx = firstHitIdx >= 0 ? Math.min(s.length, firstHitIdx + width) : Math.min(s.length, width * 2);
          s = (startIdx > 0 ? '…' : '') + s.slice(startIdx, endIdx) + (endIdx < s.length ? '…' : '');
        }
        pieceLines.push(s);
      }

      segments.push({
        lines: [start, end],               // 行号区间；start==end 表示单行
        snippet: pieceLines.join('\n'),    // 多行用换行拼接
      });
    }

    return segments;
  }

  // —— 查询 —— //
  search(query, opts = {}) {
    const {
      topK = 10,
      requireAll = true,                 // true=AND, false=OR
      snippetWidth = this.options.snippetWidth,
      mergeGap = this.options.mergeGap,
      maxDistance = null,                // 最大距离限制，超过此距离的结果将被过滤
    } = opts;

    const qTokens = this._tokenize(this._normalize(query));
    if (qTokens.length === 0) return [];

    // 准备 postings
    const postingLists = [];
    for (const term of qTokens) {
      const entry = this.inverted.get(term);
      if (!entry) {
        if (requireAll) return []; // AND：某词缺失直接无结果
        // OR：缺词允许，后续用覆盖率惩罚
        continue;
      }
      postingLists.push({ term, entry });
    }

    if (postingLists.length === 0) return []; // 所有词都未出现

    // 候选集合
    let candidateIds;
    if (requireAll) {
      // AND 交集
      const sets = postingLists.map(p => new Set(p.entry.postings.map(x => x.id)));
      candidateIds = sets[0];
      for (let i = 1; i < sets.length; i++) {
        candidateIds = new Set([...candidateIds].filter(x => sets[i].has(x)));
        if (candidateIds.size === 0) return [];
      }
    } else {
      // OR 并集
      candidateIds = new Set();
      for (const p of postingLists) {
        for (const post of p.entry.postings) candidateIds.add(post.id);
      }
    }

    const results = [];
    for (const docId of candidateIds) {
      // 收集该文档内的 positions
      const perTermPositions = [];
      let idfSum = 0;
      let presentTerms = 0;

      for (const { term, entry } of postingLists) {
        const posting = entry.postings.find(p => p.id === docId);
        if (!posting) {
          if (requireAll) { presentTerms = -1; break; }
          continue; // OR：允许缺失
        }
        perTermPositions.push(posting.positions);
        idfSum += (this.idf.get(term) || 0);
        presentTerms++;
      }
      if (presentTerms <= 0) continue; // AND下缺失或OR下完全没命中（不太可能）

      // 距离：最小覆盖窗口（至少两词时有意义）
      const minWindow = perTermPositions.length >= 2
        ? minimalWindowSpan(perTermPositions)
        : 1000;

      // 注意：距离过滤现在在片段级别进行，这里不再在文档级别过滤
      // 这样可以确保只有距离符合要求的片段才会被包含在结果中

      // 覆盖率（仅 OR 有效；AND 时=1）
      const coverage = requireAll ? 1 : (presentTerms / qTokens.length);
      const coveragePenalty = requireAll ? 1 : Math.pow(coverage, 2);

      // 邻近度分（以 presentTerms 作为相邻基线）
      const proximityScore =
        1 / (1 + Math.max(0, minWindow - (Math.max(1, presentTerms) - 1)));

      // 词频 tie-breaker
      const tfTotal = perTermPositions.reduce((s, arr) => s + arr.length, 0);

      const score = coveragePenalty
        * proximityScore
        * (1 + Math.log(1 + tfTotal))
        * (1 + idfSum);

      const docText = this.docs.get(docId)[this.options.textField];
      const snippets = this._collectSnippets(docText, qTokens, snippetWidth, mergeGap, maxDistance);

      // 没命中行就不要这个文档（严格符合你的要求）
      if (snippets.length === 0) continue;

      results.push({
        id: docId,
        coverage,
        proximity: minWindow,
        tfTotal,
        score,
        snippets, // [{ lines:[start,end], snippet:string }, ...]
        doc: this.docs.get(docId),
      });
    }

    // 排序：覆盖率 > 距离(小优) > 分数 > 词频
    results.sort((a, b) => {
      if (a.coverage !== b.coverage) return b.coverage - a.coverage;
      if (a.proximity !== b.proximity) return a.proximity - b.proximity;
      if (a.score !== b.score) return b.score - a.score;
      return b.tfTotal - a.tfTotal;
    });

    return results.slice(0, topK);
  }
}

/**
 * 计算最小覆盖窗口（所有 term 至少出现一次的最短 token 跨度）
 * positionsList: Array<Array<number>>，每个数组为一个 term 在该文档中的位置序列
 * 返回：endPos - startPos 的跨度（非长度）
 */
function minimalWindowSpan(positionsList) {
  const merged = [];
  positionsList.forEach((arr, i) => {
    arr.forEach(pos => merged.push({ pos, i }));
  });
  merged.sort((a, b) => a.pos - b.pos);

  const need = positionsList.length;
  const count = new Map();
  let have = 0;
  let left = 0;
  let best = Infinity;

  for (let right = 0; right < merged.length; right++) {
    const r = merged[right];
    count.set(r.i, (count.get(r.i) || 0) + 1);
    if (count.get(r.i) === 1) have++;

    while (have === need && left <= right) {
      const span = merged[right].pos - merged[left].pos;
      if (span < best) best = span;

      const l = merged[left];
      count.set(l.i, count.get(l.i) - 1);
      if (count.get(l.i) === 0) have--;
      left++;
    }
  }
  return Number.isFinite(best) ? best : 1e9;
}

module.exports = { CNFullText };
