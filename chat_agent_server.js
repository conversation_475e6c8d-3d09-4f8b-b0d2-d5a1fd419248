#!/usr/bin/env node

const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

// 导入chat-agent.js的功能
const {
    getDocumentPathContents,
    manageContext,
    estimateTokens,
    parseToolCall,
    parseSearchFilesInfo,
    listFiles,
    readFile,
    searchFiles,
    searchDocuments,
    executeMcpTool,
    executeFullIndexSearch
} = require('./chat-agent.js');

// 全文索引通过chat-agent.js模块自动初始化

// 初始化全文索引
async function initializeFullIndex() {
    // 直接调用chat-agent.js中的初始化函数
    try {
        // 由于chat-agent.js在require时会自动初始化，我们只需要确保它被正确加载
        console.log('全文索引已通过chat-agent.js模块初始化');
        return Promise.resolve();
    } catch (error) {
        console.error('全文索引初始化失败:', error.message);
        return Promise.reject(error);
    }
}

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.static(path.join(__dirname, 'public')));

// 读取配置文件
const configPath = path.join(__dirname, 'config.json');
const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

// 从模板文件读取系统提示词
const systemPromptPath = path.join(__dirname, 'system-prompt.txt');
let systemPrompt = '';
if (fs.existsSync(systemPromptPath)) {
    systemPrompt = fs.readFileSync(systemPromptPath, 'utf8');
} else {
    console.error("错误：找不到系统提示词文件 'system-prompt.txt'");
    process.exit(1);
}

// 构建发送给API的消息历史
async function buildMessagesForApi(conversationHistory) {
    const messages = [];
    
    // 添加系统提示词
    messages.push({
        role: 'system',
        content: systemPrompt
    });
    
    // 添加所有对话历史
    conversationHistory.forEach(msg => {
        if (msg.role === 'user' || msg.role === 'assistant' || msg.role === 'tool') {
            messages.push({
                role: msg.role,
                content: msg.content
            });
        }
    });
    
    // 获取documentPath目录下的所有文件和目录，并添加到最新的用户消息中
    if (messages.length > 0 && messages[messages.length - 1].role === 'user') {
        const environmentDetail = getDocumentPathContents();
        messages[messages.length - 1].content += '\n\n' + environmentDetail;
    }
    
    // 管理上下文长度
    const managedMessages = await manageContext(messages);
    
    return managedMessages;
}

// 调用API获取模型响应，带重试机制
async function callModelApi(messages, retryCount = 0) {
    const maxRetries = 3;
    const retryDelay = 1000; // 1秒
    
    try {
        const fetch = (await import('node-fetch')).default;
        const baseUrl = config.api.baseUrl;
        const modelId = config.api.modelId;
        const apiKey = config.api.apiKey;
        
        const response = await fetch(`${baseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`,
                'Accept': 'text/event-stream'
            },
            body: JSON.stringify({
                model: modelId,
                messages: messages,
                temperature: 0.3,
                max_tokens: 4096,
                stream: true
            })
        });
        
        if (!response.ok) {
            if (retryCount < maxRetries) {
                console.log(`API请求失败，将在${retryDelay}ms后重试 (${retryCount + 1}/${maxRetries})`);
                await new Promise(resolve => setTimeout(resolve, retryDelay));
                return callModelApi(messages, retryCount + 1);
            }
            throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }
        
        return response;
    } catch (error) {
        if (retryCount < maxRetries) {
            console.log(`API调用出错，将在${retryDelay}ms后重试 (${retryCount + 1}/${maxRetries})`, error.message);
            await new Promise(resolve => setTimeout(resolve, retryDelay));
            return callModelApi(messages, retryCount + 1);
        }
        throw error;
    }
}

// 处理流式响应并收集完整内容
async function collectStreamResponse(response, res = null) {
    console.log('🔄 开始处理流式响应...');
    return new Promise((resolve, reject) => {
        let accumulatedContent = '';
        let chunkCount = 0;
        let buffer = ''; // 用于处理跨包的数据

        response.body.on('data', (chunk) => {
            chunkCount++;
            buffer += chunk.toString();

            // 处理完整的行
            const lines = buffer.split('\n');
            buffer = lines.pop() || ''; // 保留最后一个可能不完整的行

            for (const line of lines) {
                if (line.trim() && line.startsWith('data: ')) {
                    const data = line.slice(6).trim();
                    if (data === '[DONE]') {
                        console.log('🏁 模型API流式响应结束，总内容长度:', accumulatedContent.length);
                        // 注意：这里不向前端发送[DONE]，因为可能还有工具调用需要处理
                        resolve(accumulatedContent);
                        return;
                    }

                    // 跳过空数据
                    if (!data) continue;

                    try {
                        const parsed = JSON.parse(data);
                        const content = parsed.choices[0]?.delta?.content || '';
                        if (content) {
                            accumulatedContent += content;

                            // 如果有res参数，进行流式输出处理
                            if (res) {
                                res.write(`data: ${JSON.stringify({
                                    choices: [{
                                        delta: { content: content }
                                    }]
                                })}\n\n`);
                            }
                        }
                    } catch (e) {
                        // 只在调试模式下记录JSON解析错误，避免日志污染
                        if (process.env.DEBUG) {
                            console.log('❌ JSON解析错误:', e.message, '数据:', data.substring(0, 100));
                        }
                    }
                }
            }
        });

        response.body.on('end', () => {
            resolve(accumulatedContent);
        });

        response.body.on('error', (error) => {
            reject(error);
        });
    });
}



// 执行工具，带重试机制
async function executeTool(toolInfo, retryCount = 0) {
    const maxRetries = 3;
    const retryDelay = 1000; // 1秒
    
    try {
        let result = "";
        
        switch (toolInfo.name) {
            case '查资料':
                result = searchDocuments(toolInfo.paramValue);
                break;
                
            case 'list_files':
                result = listFiles(toolInfo.paramValue);
                break;
                
            case 'read_file':
                if (typeof toolInfo.paramValue === 'object' && toolInfo.paramValue !== null) {
                    result = readFile(toolInfo.paramValue.path, toolInfo.paramValue.line);
                } else {
                    result = readFile(toolInfo.paramValue);
                }
                break;
                
            case 'search_files':
                result = searchFiles(toolInfo.fullContent);
                break;

            case 'full_index_search':
                result = await executeFullIndexSearch(toolInfo.paramValue);
                break;

            default:
                // 检查是否为MCP工具
                if (toolInfo.isMcpTool) {
                    result = await executeMcpTool(toolInfo);
                } else {
                    result = "未知工具";
                }
        }
        
        return result;
    } catch (error) {
        if (retryCount < maxRetries) {
            console.log(`工具执行失败，将在${retryDelay}ms后重试 (${retryCount + 1}/${maxRetries})`, error.message);
            await new Promise(resolve => setTimeout(resolve, retryDelay));
            return executeTool(toolInfo, retryCount + 1);
        }
        throw error;
    }
}

// 发送调查轮次消息到前端
function sendRoundMessage(res, roundNumber) {
    const roundMessage = `<investigation round ${roundNumber}>`;
    res.write(`data: ${JSON.stringify({
        type: 'round_marker',
        content: roundMessage,
        round: roundNumber
    })}\n\n`);
    console.log(`📍 发送调查轮次标记: ${roundMessage}`);
}

// 发送调查完成消息到前端
function sendInvestigationFinished(res) {
    const finishedMessage = '<investigation finished>';
    res.write(`data: ${JSON.stringify({
        type: 'investigation_finished',
        content: finishedMessage
    })}\n\n`);
    console.log(`🏁 发送调查完成标记: ${finishedMessage}`);
}

// 处理完整的对话流程
async function processConversation(conversationHistory, res) {
    let currentHistory = [...conversationHistory];
    let retryCount = 0;
    let currentRound = 1;

    // 发送第一轮调查开始消息
    sendRoundMessage(res, currentRound);

    while (true) {
        try {
            // 构建API请求消息
            const messages = await buildMessagesForApi(currentHistory);

            // 调用模型API
            console.log('🚀 调用模型API，消息数量:', messages.length);
            const response = await callModelApi(messages);
            console.log('📡 收到模型响应，开始处理流式数据...');

            // 收集完整响应，支持流式输出和think标签处理
            const assistantMessage = await collectStreamResponse(response, res);
            console.log('✅ 模型响应完成，内容长度:', assistantMessage.length);

            if (!assistantMessage.trim()) {
                throw new Error('模型返回空响应');
            }

            // 模型响应完成，继续处理工具调用

            // 添加助手消息到历史
            currentHistory.push({
                role: 'assistant',
                content: assistantMessage
            });

            // 解析工具调用
            const toolInfo = parseToolCall(assistantMessage);

            if (!toolInfo) {
                // 没有工具调用，可能是直接回复，重试
                res.write(`data: ${JSON.stringify({
                    type: 'retry',
                    content: '模型输出中未检测到有效工具调用，重新请求模型...'
                })}\n\n`);
                retryCount++;
                continue;
            }

            // 生成工具调用描述
            let toolDescription = '';
            if (toolInfo.name === 'search_files') {
                const searchInfo = parseSearchFilesInfo(toolInfo.fullContent);
                toolDescription = `执行搜索文件操作，目录/文件名: ${searchInfo.path}，搜索内容: ${searchInfo.regex}`;
            } else if (toolInfo.name === 'list_files') {
                toolDescription = `执行列出目录操作，目录/文件名: ${toolInfo.paramValue}`;
            } else if (toolInfo.name === 'read_file') {
                const filePath = typeof toolInfo.paramValue === 'object' ? toolInfo.paramValue.path : toolInfo.paramValue;
                toolDescription = `执行读取文件操作，目录/文件名: ${filePath}`;
            } else if (toolInfo.name === '查资料') {
                const keywords = Array.isArray(toolInfo.paramValue) ? toolInfo.paramValue.join(', ') : toolInfo.paramValue;
                toolDescription = `执行查询资料操作，目录/文件名: 知识库，关键词: ${keywords}`;
            } else if (toolInfo.name === 'full_index_search') {
                const keywords = toolInfo.paramValue?.keywords || '';
                toolDescription = `执行全文搜索操作，目录/文件名: 全文索引，关键词: ${keywords}`;
            } else {
                toolDescription = `执行 ${toolInfo.name} 操作`;
            }

            // 检查是否是最终工具
            const isFinalTool = ['回答问题', '提问', '转人工'].includes(toolInfo.name);

            if (isFinalTool) {
                console.log(`🎯 检测到最终工具: ${toolInfo.name}`);

                // 发送调查完成消息
                sendInvestigationFinished(res);

                // 发送最终工具的内容作为流式输出
                const finalContent = extractFinalToolContent(assistantMessage, toolInfo.name);
                if (finalContent) {
                    console.log(`📤 发送最终工具内容，长度: ${finalContent.length}`);
                    // 将最终内容分块发送，模拟流式输出
                    for (let i = 0; i < finalContent.length; i += 5) {
                        const chunk = finalContent.slice(i, i + 5);
                        res.write(`data: ${JSON.stringify({
                            choices: [{
                                delta: { content: chunk }
                            }]
                        })}\n\n`);
                    }
                }

                // 发送结束信号
                res.write('data: [DONE]\n\n');
                res.end();
                return;
            }

            // 执行工具
            const toolResult = await executeTool(toolInfo);

            // 生成简洁的工具执行结果描述
            let resultDescription = '';
            if (toolInfo.name === 'list_files') {
                const fileCount = toolResult.split('\n').filter(line => line.trim()).length;
                resultDescription = `成功，找到 ${fileCount} 条信息`;
            } else if (toolInfo.name === 'read_file') {
                const lineCount = toolResult.split('\n').length;
                resultDescription = `成功，找到 ${lineCount} 条信息`;
            } else if (toolInfo.name === 'search_files') {
                const matches = toolResult.split('文件: ').length - 1;
                resultDescription = `成功，找到 ${matches} 条信息`;
            } else if (toolInfo.name === '查资料') {
                const resultLines = toolResult.split('\n').filter(line => line.trim()).length;
                resultDescription = `成功，找到 ${resultLines} 条信息`;
            } else if (toolInfo.name === 'full_index_search') {
                const matches = toolResult.split('文件: ').length - 1;
                resultDescription = `成功，找到 ${matches} 条信息`;
            } else {
                const resultLines = toolResult.split('\n').filter(line => line.trim()).length;
                resultDescription = `成功，找到 ${resultLines} 条信息`;
            }

            // 将工具执行总结作为流式内容发送给前端
            const toolSummary = `\n\n🔧 ${toolDescription}\n✅ ${resultDescription}`;

            // 将总结内容分块发送，模拟流式输出
            for (let i = 0; i < toolSummary.length; i += 10) {
                const chunk = toolSummary.slice(i, i + 10);
                res.write(`data: ${JSON.stringify({
                    choices: [{
                        delta: { content: chunk }
                    }]
                })}\n\n`);
            }

            // 添加工具结果到历史
            currentHistory.push({
                role: 'tool',
                content: `[${toolInfo.name} ${toolInfo.paramValue || ''}] result: ${toolResult}`
            });

            // 准备进入下一轮调查
            currentRound++;
            console.log(`🔄 准备进入第 ${currentRound} 轮调查`);

            // 发送下一轮调查开始消息
            sendRoundMessage(res, currentRound);

            // 继续下一轮对话

        } catch (error) {
            console.error('❌ 处理对话错误:', error);
            res.write('data: [DONE]\n\n');
            res.end();
            return;
        }
    }
}

// 提取最终工具的内容
function extractFinalToolContent(response, toolName) {
    try {
        if (toolName === '提问') {
            const questionMatch = response.match(/<question>(.*?)<\/question>/s);
            return questionMatch ? questionMatch[1].trim() : '';
        } else if (toolName === '回答问题') {
            const answerMatch = response.match(/<answer>(.*?)<\/answer>/s);
            return answerMatch ? answerMatch[1].trim() : '';
        } else if (toolName === '转人工') {
            const transferMatch = response.match(/<reason>(.*?)<\/reason>/s);
            return transferMatch ? `转人工原因：${transferMatch[1].trim()}` : '正在转接人工客服...';
        }
        return '';
    } catch (error) {
        console.error('❌ 提取最终工具内容失败:', error);
        return '';
    }
}

// 聊天API端点
app.post('/api/chat', async (req, res) => {
    try {
        console.log('🎯 收到聊天请求');
        const { messages: conversationHistory } = req.body;

        if (!conversationHistory || !Array.isArray(conversationHistory)) {
            console.log('❌ 无效的对话历史格式');
            return res.status(400).json({ error: '无效的对话历史格式' });
        }

        console.log('📨 消息数量:', conversationHistory.length);
        console.log('📝 最后一条消息:', conversationHistory[conversationHistory.length - 1]?.content?.substring(0, 100) + '...');

        // 设置流式响应
        res.setHeader('Content-Type', 'text/event-stream');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');

        console.log('🔗 SSE连接已建立');

        // 开始处理对话
        await processConversation(conversationHistory, res);

    } catch (error) {
        console.error('❌ 聊天API错误:', error);
        if (!res.headersSent) {
            res.status(500).json({ error: `处理请求时出现错误: ${error.message}` });
        }
    }
});

// 根路径重定向到index.html
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 健康检查端点
app.get('/api/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 启动服务器
app.listen(PORT, async () => {
    console.log(`Chat Agent Server 正在运行在 http://localhost:${PORT}`);
    console.log(`API端点: http://localhost:${PORT}/api/chat`);
    console.log(`健康检查: http://localhost:${PORT}/api/health`);

    // 初始化全文索引
    try {
        await initializeFullIndex();
    } catch (error) {
        console.error('全文索引初始化失败:', error.message);
    }
});

// 导出测试需要的函数
app.__test__ = {
  callModelApi,
  executeTool
};

module.exports = app;
