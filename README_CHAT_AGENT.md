# 智能体对话系统 (chat-agent.js)

这是一个基于Node.js的智能体对话系统，支持命令行和Web界面两种交互方式，使用OpenAI兼容的API服务进行对话。

## 功能特性

1. 支持命令行和Web界面两种交互方式
2. 使用OpenAI兼容的API服务进行对话
3. 支持多种工具调用：
   - 提问：当需要特定信息才能继续时使用
   - 查资料：根据关键词检索相关文档
   - list_files：列出指定目录下的文件和目录
   - read_file：读取指定文件的内容
   - search_files：在指定目录中执行正则表达式搜索
   - 转人工：当无法解决问题时转接人工客服
   - 回答问题：根据检索到的信息给出最终答复
   - MCP工具：通过配置的MCP服务器扩展系统能力
4. 上下文管理：自动管理对话历史长度，确保不超过token限制
5. 相关性筛选：对大量工具结果进行相关性筛选，只保留与用户问题相关的内容
6. MCP功能：支持配置多个MCP服务器以扩展系统能力

## 安装依赖

```bash
npm install
```

## 配置

在 `config.json` 文件中配置API信息和其他设置：

```json
{
  "api": {
    "baseUrl": "https://aip.b.qianxin-inc.cn/v1",
    "modelId": "DeepSeek-V3-0324",
    "apiKey": "your-api-key"
  },
  "compressionApi": {
    "baseUrl": "https://aip.b.qianxin-inc.cn/v2",
    "modelId": "DeepSeek-V3-0324",
    "apiKey": "your-api-key"
  },
  "services": {
    "documentPath": "./documents",
    "knowledgeBasePath": "./knowledge"
  },
  "tools": {
    "提问": {
      "description": "当需要特定信息才能继续时使用（用户未提供足够细节时）"
    },
    "查资料": {
      "description": "当用户问题包含明确关键词时直接检索, 需要提炼关键词以提高召回的几率"
    },
    "读目录文件": {
      "description": "需要确定资料分类时使用"
    },
    "read_file": {
      "description": "已有明确文件路径时获取详细内容"
    },
    "search_files": {
      "description": "当文档目录下文件过多时，可以使用此在指定目录中执行正则表达式搜索，以找到合适的文件",
      "contextLines": 5
    },
    "转人工": {
      "description": "当经过多次尝试后仍然无法找到答案，使用此工具。注意必须是已经尝试所有目录和文件后仍然无法找到答案时才使用此工具。"
    },
    "回答问题": {
      "description": "当确认掌握足够信息时给出最终答复，不能依据常识或推理回答，必须严格根据文件内容回答，同时给出参考文件的名称"
    }
  },
  "mcp": {
    "servers": [
      {
        "name": "weather-server",
        "path": "/path/to/weather-mcp-server"
      },
      {
        "name": "github-server",
        "path": "/path/to/github-mcp-server"
      }
    ]
  },
  "context": {
    "maxTokens": 128000
  },
  "productDescriptionFile": "./info.txt"
}
```

### MCP服务器配置

MCP（Model Context Protocol）服务器可以为系统提供额外的工具和资源来扩展系统能力。您可以在`config.json`文件的`mcp.servers`数组中配置多个MCP服务器。

每个MCP服务器配置项包含以下属性：
- `name`：服务器的唯一标识符
- `path`：服务器的路径或地址

## 使用方法

### 命令行界面

```bash
# 启动命令行对话系统
node chat-agent.js
```

在命令行界面中，您可以直接输入问题与智能体对话。输入`quit`退出程序。

### Web界面

```bash
# 在浏览器中打开chat-agent.html文件
open chat-agent.html
```

在Web界面中，您需要先设置API信息，然后可以在输入框中输入问题与智能体对话。

## 工具说明

### 提问工具
当需要特定信息才能继续时使用。

### 查资料工具
根据关键词检索相关文档。

### list_files工具
列出指定目录下的文件和目录。

### read_file工具
读取指定文件的内容，支持指定行号范围。

### search_files工具
在指定目录中执行正则表达式搜索。

### 转人工工具
当无法解决问题时转接人工客服。

### 回答问题工具
根据检索到的信息给出最终答复。

### MCP工具
通过配置的MCP服务器扩展系统能力。MCP服务器可以提供额外的工具和资源。

## 开发指南

### 运行测试

```bash
npm test
```

### 代码结构

- `chat-agent.js`：主程序文件，包含所有核心功能
- `config.json`：配置文件
- `system-prompt.txt`：系统提示词
- `info.txt`：产品描述文件
- `documents/`：文档目录
- `knowledge/`：知识库目录
- `test/`：测试文件目录

## 扩展功能

### MCP功能
系统支持通过MCP协议连接到外部服务器，以获取额外的工具和资源。要使用此功能：

1. 在`config.json`中配置MCP服务器
2. 系统启动时会自动连接到配置的MCP服务器
3. 连接成功后，MCP服务器提供的工具将自动添加到可用工具列表中
4. 智能体可以调用这些MCP工具来执行额外的操作

MCP功能使系统能够轻松扩展其能力，而无需修改核心代码。
