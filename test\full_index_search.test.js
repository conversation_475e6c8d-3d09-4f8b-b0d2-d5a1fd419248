const { executeFullIndexSearch, initializeFullIndex } = require('../chat-agent.js');
const path = require('path');

async function testFullIndexSearch() {
    console.log('开始测试 full_index_search 功能...\n');

    // 初始化全文索引引擎
    try {
        await initializeFullIndex();
        console.log('全文索引引擎初始化完成\n');
    } catch (error) {
        console.error('全文索引引擎初始化失败:', error);
        process.exit(1);
    }
    
    // 测试用例1：基本功能测试
    console.log('测试用例1：搜索"终端安全"路径下的"Android 管控"');
    const test1 = await executeFullIndexSearch({
        keywords: ["Android 管控"],
        path: "终端安全"
    });
    console.log('测试结果1:', test1);
    console.log('----------------------------------------\n');

    // 测试用例2：验证路径匹配逻辑
    console.log('测试用例2：验证路径匹配逻辑 - 带斜杠的路径');
    const test2 = await executeFullIndexSearch({
        keywords: ["Android"],
        path: "终端安全/"
    });
    console.log('测试结果2:', test2);
    console.log('----------------------------------------\n');

    // 测试用例3：验证中文分词
    console.log('测试用例3：验证中文分词 - 单个中文字符');
    const test3 = await executeFullIndexSearch({
        keywords: ["管控"],
        path: "终端安全"
    });
    console.log('测试结果3:', test3);
    console.log('----------------------------------------\n');

    // 测试用例4：验证多关键词搜索
    console.log('测试用例4：验证多关键词搜索');
    const test4 = await executeFullIndexSearch({
        keywords: ["Android", "管控"],
        path: "终端安全"
    });
    console.log('测试结果4:', test4);
    console.log('----------------------------------------\n');

    // 测试用例5：验证大小写敏感性
    console.log('测试用例5：验证大小写敏感性 - 搜索"ANDROID 管控"');
    const test5 = await executeFullIndexSearch({
        keywords: ["ANDROID 管控"],
        path: "终端安全"
    });
    console.log('测试结果5:', test5);
    console.log('----------------------------------------\n');

    // 测试用例6：验证"终端安全"路径下"Android 强管控"的搜索
    console.log('测试用例6：验证"终端安全"路径下"Android 强管控"的搜索');
    const test6 = await executeFullIndexSearch({
        keywords: ["Android 强管控"],
        path: "终端安全"
    });
    console.log('测试结果6:', test6);
    console.log('----------------------------------------\n');

    console.log('测试完成');
}

testFullIndexSearch().catch(err => {
    console.error('测试失败:', err);
});
