{"api": {"baseUrl": "https://aip.b.qianxin-inc.cn/v1", "modelId": "Qwen3-235B-2507-H", "apiKey": "33147eaa9875abac5f7fd5a1aa830d0bfd486c8b"}, "compressionApi": {"baseUrl": "https://aip.b.qianxin-inc.cn/v2", "modelId": "Qwen2.5-VL-32B-Instruct", "apiKey": "33147eaa9875abac5f7fd5a1aa830d0bfd486c8b"}, "services": {"documentPath": "d:/documents", "fullIndexDbPath": "./index_databases"}, "tools": {"提问": {"description": "当需要特定信息才能继续时使用（用户未提供足够细节时）"}, "查资料": {"description": "当用户问题包含明确关键词时直接检索, 需要提炼关键词以提高召回的几率"}, "读目录文件": {"description": "需要确定资料分类时使用"}, "read_file": {"description": "已有明确文件路径时获取详细内容"}, "search_files": {"description": "当文档目录下文件过多时，可以使用此在指定目录中执行正则表达式搜索，以找到合适的文件", "contextLines": 5}, "full_index_search": {"description": "使用全文索引搜索文档内容，支持中英文关键词搜索，比search_files更快更准确，必须指定搜索目录", "lineLimit": 600, "max_distance": 20}, "转人工": {"description": "当经过多次尝试后仍然无法找到答案，使用此工具。注意必须是已经尝试所有目录和文件后仍然无法找到答案时才使用此工具。"}, "回答问题": {"description": "当确认掌握足够信息时给出最终答复，不能依据常识或推理回答，必须严格根据文件内容回答，同时给出参考文件的名称"}}, "mcp": {"servers": []}, "context": {"maxTokens": 100000}, "productDescriptionFile": "./info.txt", "multimodalApi": {"baseUrl": "https://aip.b.qianxin-inc.cn/v1", "modelId": "Qwen2.5-VL-32B-Instruct", "apiKey": "33147eaa9875abac5f7fd5a1aa830d0bfd486c8b"}}