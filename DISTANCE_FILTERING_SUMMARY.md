# 全文检索片段级别距离过滤功能

## 问题描述

原来的实现中，如果整个文档中有一处符合 `max_distance` 限制，则该文档的所有命中片段都会被纳入结果，包括那些关键词距离很远的片段。

## 解决方案

实现了片段级别的距离过滤，确保只有关键词距离符合要求的片段才会被包含在搜索结果中。

## 主要修改

### 1. 修改 `_collectSnippets` 方法 (indexer.js)

- 添加了 `maxDistance` 参数
- 在片段级别检查关键词距离
- 使用字符位置计算距离，比分词位置更准确
- 将字符距离转换为大致的词距离（除以2）

### 2. 添加 `_calculateCharMinWindow` 方法 (indexer.js)

- 计算字符级别的最小覆盖窗口
- 考虑词的长度，计算从第一个词开始到最后一个词结束的距离
- 使用滑动窗口算法找到最小覆盖距离

### 3. 修改搜索逻辑 (indexer.js)

- 将 `maxDistance` 参数传递给 `_collectSnippets` 方法
- 移除了文档级别的距离过滤，改为片段级别过滤

## 工作原理

1. **片段生成**：首先按照原有逻辑生成包含关键词的片段
2. **距离检查**：对每个片段，计算其中关键词的字符位置
3. **距离计算**：使用字符级别的最小覆盖窗口算法计算距离
4. **距离转换**：将字符距离除以2，得到大致的词距离
5. **过滤决策**：如果词距离超过 `maxDistance`，则跳过该片段

## 配置

在 `config.json` 中设置：

```json
{
  "tools": {
    "full_index_search": {
      "max_distance": 20
    }
  }
}
```

## 测试验证

创建了测试文件验证功能：

- `test_distance_filtering.js`：测试基本的距离过滤功能
- `test_full_index_integration.js`：测试与 fullIndexSearch 的集成

## 效果

- **距离限制 <= 5**：只保留关键词距离较近的片段
- **距离限制 <= 2**：只保留关键词非常接近的片段
- **不限制距离**：保留所有包含关键词的片段

## 优势

1. **精确过滤**：只保留真正相关的片段，提高搜索质量
2. **字符级精度**：使用字符位置而非分词位置，更准确
3. **保持兼容**：不影响现有的搜索功能
4. **可配置**：通过配置文件灵活调整距离阈值
