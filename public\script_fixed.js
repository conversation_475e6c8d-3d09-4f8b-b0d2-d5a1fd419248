// ChatGPT 风格的聊天界面 - 修复版
class ChatApp {
    constructor() {
        this.messages = [];
        this.currentStreamingMessage = null;
        this.currentInvestigationBox = null;
        this.isStreaming = false;
        this.fullContent = '';
        this.currentSessionId = null;
        this.sessions = [];
        
        this.initElements();
        this.bindEvents();
        this.autoResizeTextarea();
        this.loadSessions();
        this.initNewSession();
    }

    // 生成UUID
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    // 加载会话
    loadSessions() {
        try {
            const sessions = localStorage.getItem('chatSessions');
            if (sessions) {
                this.sessions = JSON.parse(sessions);
            }
        } catch (e) {
            console.error('加载会话失败:', e);
            this.sessions = [];
        }
    }

    // 保存会话
    saveSessions() {
        try {
            localStorage.setItem('chatSessions', JSON.stringify(this.sessions));
        } catch (e) {
            console.error('保存会话失败:', e);
        }
    }

    // 初始化新会话
    initNewSession() {
        // 清空当前消息
        this.clearMessages();
        
        // 生成新的会话ID
        this.currentSessionId = this.generateUUID();
        
        // 创建新会话对象
        const newSession = {
            id: this.currentSessionId,
            title: '新对话',
            messages: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        // 添加到会话列表
        this.sessions.unshift(newSession);
        
        // 保存会话
        this.saveSessions();
        
        // 更新会话列表显示
        this.updateSessionList();
    }

    // 新建会话
    newSession() {
        this.initNewSession();
    }

    // 清空消息
    clearMessages() {
        // 保留欢迎消息，清除其他消息
        const messageGroups = this.messages.querySelectorAll('.message-group');
        messageGroups.forEach((group, index) => {
            if (index > 0) { // 保留第一个欢迎消息
                group.remove();
            }
        });
        
        this.currentStreamingMessage = null;
        this.isStreaming = false;
        this.updateSendButton();
    }

    // 更新会话列表显示
    updateSessionList() {
        const chatHistory = document.getElementById('chatHistory');
        if (!chatHistory) return;
        
        // 清空现有内容
        chatHistory.innerHTML = '';
        
        // 添加会话列表
        this.sessions.forEach((session, index) => {
            const sessionElement = document.createElement('div');
            sessionElement.className = 'session-item';
            sessionElement.dataset.sessionId = session.id;
            
            sessionElement.innerHTML = `
                <div class="session-title">${session.title}</div>
                <div class="session-actions">
                    <button class="delete-btn" title="删除会话">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 6h18M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6m3 0V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        </svg>
                    </button>
                </div>
            `;
            
            // 添加点击事件
            sessionElement.addEventListener('click', (e) => {
                if (!e.target.closest('.delete-btn')) {
                    this.loadSession(session.id);
                }
            });
            
            // 添加删除按钮事件
            const deleteBtn = sessionElement.querySelector('.delete-btn');
            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.deleteSession(session.id);
            });
            
            chatHistory.appendChild(sessionElement);
        });
    }

    // 加载会话
    loadSession(sessionId) {
        const session = this.sessions.find(s => s.id === sessionId);
        if (!session) return;
        
        // 设置当前会话ID
        this.currentSessionId = sessionId;
        
        // 清空当前消息
        this.clearMessages();
        
        // 加载会话消息
        session.messages.forEach(msg => {
            if (msg.role === 'user') {
                this.addUserMessage(msg.content);
            } else if (msg.role === 'assistant') {
                this.addAssistantMessageFromHistory(msg.content);
            }
        });
    }

    // 从历史记录添加助手消息
    addAssistantMessageFromHistory(content) {
        const messageGroup = document.createElement('div');
        messageGroup.className = 'message-group assistant';
        
        messageGroup.innerHTML = `
            <div class="message-avatar">
                <div class="avatar-circle">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                </div>
            </div>
            <div class="message-content">
                <div class="message-text">${this.formatMessage(content)}</div>
            </div>
        `;
        
        this.messages.appendChild(messageGroup);
        this.scrollToBottom();
    }

    // 删除会话
    deleteSession(sessionId) {
        if (this.sessions.length <= 1) {
            alert('至少需要保留一个会话');
            return;
        }
        
        if (confirm('确定要删除这个会话吗？')) {
            this.sessions = this.sessions.filter(s => s.id !== sessionId);
            this.saveSessions();
            this.updateSessionList();
            
            // 如果删除的是当前会话，加载第一个会话
            if (this.currentSessionId === sessionId) {
                if (this.sessions.length > 0) {
                    this.loadSession(this.sessions[0].id);
                } else {
                    this.initNewSession();
                }
            }
        }
    }

    initElements() {
        this.messagesContainer = document.getElementById('messagesContainer');
        this.messages = document.getElementById('messages');
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendBtn');
    }

    bindEvents() {
        this.sendButton.addEventListener('click', () => this.sendMessage());

        this.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 新会话按钮事件
        document.getElementById('newChatBtn').addEventListener('click', () => this.newSession());

        // 初始化按钮状态
        this.updateSendButtonState();
    }

    autoResizeTextarea() {
        this.messageInput.addEventListener('input', () => {
            this.messageInput.style.height = 'auto';
            this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 200) + 'px';
            this.updateSendButtonState();
        });
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message || this.isStreaming) return;

        // 添加用户消息
        this.addUserMessage(message);
        this.messageInput.value = '';
        this.messageInput.style.height = 'auto';

        // 保存用户消息到当前会话
        this.saveMessageToSession('user', message);

        // 发送到服务器并处理响应
        await this.streamResponse(message);
    }

    addUserMessage(message) {
        const messageGroup = document.createElement('div');
        messageGroup.className = 'message-group user';
        
        messageGroup.innerHTML = `
            <div class="message-content">
                <div class="message-text">${this.formatMessage(message)}</div>
            </div>
            <div class="message-avatar">
                <div class="avatar-circle">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                </div>
            </div>
        `;
        
        this.messages.appendChild(messageGroup);
        this.scrollToBottom();
    }

    addAssistantMessage() {
        const messageGroup = document.createElement('div');
        messageGroup.className = 'message-group assistant';
        
        messageGroup.innerHTML = `
            <div class="message-avatar">
                <div class="avatar-circle">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                </div>
            </div>
            <div class="message-content">
                <div class="investigation-box" style="display: none;">
                    <div class="investigation-header">
                        <div class="investigation-icon">
                            <div class="thinking-dots">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                        <span class="investigation-text">正在调查</span>
                        <div class="investigation-toggle">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M18 15l-6-6-6 6"/>
                            </svg>
                        </div>
                    </div>
                    <div class="investigation-content"></div>
                </div>
                <div class="collapsed-investigation" style="display: none;">
                    <div class="collapsed-header">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 18l6-6-6-6"/>
                        </svg>
                        <span>已深度调查</span>
                    </div>
                </div>
                <div class="message-text"></div>
            </div>
        `;
        
        this.messages.appendChild(messageGroup);
        this.currentStreamingMessage = messageGroup.querySelector('.message-text');
        this.currentInvestigationBox = messageGroup.querySelector('.investigation-box');
        this.scrollToBottom();
        
        return messageGroup;
    }

    updateSendButton() {
        this.sendButton.disabled = this.isStreaming;
        this.sendButton.textContent = this.isStreaming ? '发送中...' : '发送';
    }

    updateSendButtonState() {
        const hasContent = this.messageInput.value.trim().length > 0;
        this.sendButton.disabled = this.isStreaming || !hasContent;
    }

    scrollToBottom() {
        this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }

    getConversationHistory() {
        const messageGroups = this.messages.querySelectorAll('.message-group');
        const history = [];
        
        messageGroups.forEach(group => {
            const isUser = group.classList.contains('user');
            const messageText = group.querySelector('.message-text');
            if (messageText && messageText.textContent.trim()) {
                history.push({
                    role: isUser ? 'user' : 'assistant',
                    content: messageText.textContent.trim()
                });
            }
        });
        
        return history;
    }

    async streamResponse(userMessage) {
        this.isStreaming = true;
        this.updateSendButton();
        this.fullContent = '';

        // 初始化二维数组存储结构
        if (!this.allInvestigationRounds) {
            this.allInvestigationRounds = []; // 二维数组：[会话][轮次]
        }

        // 开始新会话，添加到数组中
        this.currentSessionIndex = this.allInvestigationRounds.length;
        this.allInvestigationRounds.push([]); // 为新会话创建空的轮次数组

        this.currentRound = 0;
        this.roundStartPosition = 0;
        this.isShowingFinalAnswer = false;

        console.log(`🆕 开始新会话 ${this.currentSessionIndex}，总会话数: ${this.allInvestigationRounds.length}`);

        // 创建助手消息
        const messageGroup = this.addAssistantMessage();
        
        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    messages: [
                        ...this.getConversationHistory(),
                        { role: 'user', content: userMessage }
                    ]
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            await this.handleStreamResponse(response, messageGroup);
            
        } catch (error) {
            console.error('Error:', error);
            this.currentStreamingMessage.textContent = '抱歉，发生了错误，请稍后重试。';
        } finally {
            this.isStreaming = false;
            this.currentStreamingMessage = null;
            this.currentInvestigationBox = null;
            this.updateSendButton();
        }
    }

    async handleStreamResponse(response, messageGroup) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n').filter(line => line.trim() !== '');

            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const data = line.slice(6);

                    if (data === '[DONE]') {
                        console.log('收到[DONE]信号，最终内容:', this.fullContent);
                        this.finalizeMessage(messageGroup);
                        return;
                    }

                    try {
                        // 尝试解析JSON，如果失败则跳过这个数据块
                        const parsed = JSON.parse(data);

                        // 处理调查轮次标记
                        if (parsed.type === 'round_marker') {
                            console.log(`📍 收到调查轮次标记: round ${parsed.round}`);
                            this.handleRoundMarker(messageGroup, parsed.round);
                            continue;
                        }

                        // 处理调查完成标记
                        if (parsed.type === 'investigation_finished') {
                            console.log('🏁 收到调查完成标记');
                            this.handleInvestigationFinished(messageGroup);
                            continue;
                        }

                        // 处理流式内容
                        if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta && parsed.choices[0].delta.content) {
                            const content = parsed.choices[0].delta.content;
                            this.fullContent += content;
                            this.updateMessageDisplay(messageGroup);
                        }
                    } catch (e) {
                        // 静默处理JSON解析错误，避免中断流式传输
                        console.warn('JSON解析失败，跳过数据块:', data.substring(0, 50) + '...');
                    }
                }
            }
        }
    }

    updateMessageDisplay(messageGroup) {
        //console.log('🔄 更新消息显示，内容长度:', this.fullContent.length);

        const investigationBox = messageGroup.querySelector('.investigation-box');
        const investigationContent = messageGroup.querySelector('.investigation-content');
        const messageText = messageGroup.querySelector('.message-text');

        // 如果正在显示最终答案，更新message-text区域
        if (this.isShowingFinalAnswer && messageText) {
            const finalContent = this.extractFinalToolContentFromStream();
            if (finalContent) {
                messageText.innerHTML = this.formatMessage(finalContent);
            }
        }
        // 如果调查栏已显示，更新其内容
        else if (investigationBox && investigationBox.style.display === 'block') {
            this.updateInvestigationContent(investigationContent);
        }

        this.scrollToBottom();
    }

    extractFinalToolContentFromStream() {
        // 从流式内容中提取最终工具的内容
        // 这里我们需要从当前的fullContent中提取最终答案部分

        // 检查提问工具
        const questionMatch = this.fullContent.match(/<question>(.*?)<\/question>/s);
        if (questionMatch) {
            return questionMatch[1].trim();
        }

        // 检查回答问题工具
        const answerMatch = this.fullContent.match(/<answer>(.*?)<\/answer>/s);
        if (answerMatch) {
            return answerMatch[1].trim();
        }

        // 检查转人工工具
        if (this.fullContent.includes('<转人工>')) {
            return '正在为您转接人工客服...';
        }

        // 如果没有找到工具标签，返回调查完成后的内容
        const investigationFinishedPos = this.fullContent.lastIndexOf('</thinking>');
        if (investigationFinishedPos !== -1) {
            const afterInvestigation = this.fullContent.substring(investigationFinishedPos + 12);
            // 过滤掉工具标签，只保留纯文本内容
            return afterInvestigation.replace(/<[^>]*>/g, '').trim();
        }

        return '';
    }

    updateInvestigationContent(investigationContent) {
        if (!investigationContent) return;

        // 只显示当前轮次的内容（从轮次开始位置到当前位置）
        const currentRoundContent = this.getCurrentRoundContent();

        if (currentRoundContent) {
            investigationContent.innerHTML = this.formatMessage(currentRoundContent);
        }
    }

    getCurrentRoundContent() {
        // 获取当前轮次的内容（从轮次开始位置到当前位置）
        const startPos = this.roundStartPosition || 0;
        const currentContent = this.fullContent.substring(startPos);

        let displayContent = '';

        // 提取当前轮次的thinking内容
        const thinkStart = currentContent.indexOf('<thinking>');
        const thinkEnd = currentContent.indexOf('</thinking>');

        if (thinkStart !== -1) {
            if (thinkEnd !== -1) {
                displayContent = currentContent.substring(thinkStart + 10, thinkEnd);
            } else {
                displayContent = currentContent.substring(thinkStart + 10);
            }
        }

        // 添加当前轮次的工具总结
        const toolSummary = this.extractToolSummaryFromCurrentRound(currentContent);
        if (toolSummary) {
            if (displayContent) displayContent += '\n\n';
            displayContent += toolSummary;
        }

        return displayContent;
    }

    extractToolSummaryFromCurrentRound(content) {
        // 从当前轮次内容中提取工具总结
        const lines = content.split('\n');
        const toolLines = lines.filter(line => {
            const trimmed = line.trim();
            return (
                trimmed.includes('🔧') ||
                trimmed.includes('✅') ||
                (trimmed.includes('执行') && (trimmed.includes('操作') || trimmed.includes('搜索'))) ||
                trimmed.includes('成功，找到')
            );
        });

        return toolLines.length > 0 ? toolLines.join('\n') : '';
    }

    filterThinkingTags(content) {
        // 移除所有thinking标签及其内容，包括未闭合的thinking标签
        let filtered = content.replace(/<thinking>.*?<\/thinking>/gs, '');

        // 移除未闭合的thinking标签
        const lastThinkStart = filtered.lastIndexOf('<thinking>');
        if (lastThinkStart !== -1) {
            filtered = filtered.substring(0, lastThinkStart);
        }

        return filtered.trim();
    }

    hasThinkingContent() {
        return this.fullContent.includes('<thinking>');
    }

    hasFinalTool() {
        return this.fullContent.includes('<提问>') || this.fullContent.includes('<回答问题>');
    }

    showInvestigationBox(investigationBox, investigationContent) {
        investigationBox.style.display = 'block';

        // 更新调查内容
        this.updateInvestigationContent(investigationContent);

        // 绑定展开/收起功能
        this.bindInvestigationToggle(investigationBox);
    }

    bindInvestigationToggle(investigationBox) {
        const toggleButton = investigationBox.querySelector('.investigation-toggle');
        const contentArea = investigationBox.querySelector('.investigation-content');

        if (!toggleButton || toggleButton.onclick) return; // 避免重复绑定

        toggleButton.onclick = () => {
            const isExpanded = contentArea.style.display !== 'none';

            if (isExpanded) {
                // 收起
                contentArea.style.display = 'none';
                toggleButton.innerHTML = `
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M6 9l6 6 6-6"/>
                    </svg>
                `;
            } else {
                // 展开
                contentArea.style.display = 'block';
                toggleButton.innerHTML = `
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M18 15l-6-6-6 6"/>
                    </svg>
                `;
            }
        };
    }



    extractThinkingContent() {
        // 找到最后一个thinking标签的开始位置
        const lastThinkStart = this.fullContent.lastIndexOf('<thinking>');
        if (lastThinkStart === -1) {
            return '';
        }

        const afterThinkStart = lastThinkStart + 10;
        const nextThinkEnd = this.fullContent.indexOf('</thinking>', afterThinkStart);

        if (nextThinkEnd === -1) {
            // 未闭合的thinking标签 - 流式显示正在接收的内容
            const currentThinking = this.fullContent.substring(afterThinkStart).trim();
            console.log('流式thinking内容长度:', currentThinking.length);
            return currentThinking;
        } else {
            // 已闭合的thinking标签 - 显示完整内容
            const completedThinking = this.fullContent.substring(afterThinkStart, nextThinkEnd).trim();
            console.log('完整thinking内容长度:', completedThinking.length);
            return completedThinking;
        }
    }

    extractToolSummaryFromContent() {
        // 提取工具执行总结（后端发送的工具描述）
        const lines = this.fullContent.split('\n');
        const toolLines = [];

        for (const line of lines) {
            const trimmed = line.trim();

            // 跳过thinking标签内的内容
            if (trimmed.includes('<thinking>') || trimmed.includes('</thinking>')) {
                continue;
            }

            // 匹配工具执行描述
            if (
                trimmed.includes('🔧') ||
                trimmed.includes('✅') ||
                (trimmed.includes('执行') && (trimmed.includes('操作') || trimmed.includes('搜索') || trimmed.includes('文件'))) ||
                trimmed.includes('成功，找到') ||
                trimmed.includes('执行搜索') ||
                trimmed.includes('执行读取') ||
                trimmed.includes('执行全文')
            ) {
                toolLines.push(trimmed);
            }
        }

        const result = toolLines.join('\n');
        if (result) {
            console.log('提取到工具总结:', toolLines.length, '行');
        }
        return result;
    }

    showFinalAnswer(messageGroup, messageText) {
        // 折叠调查栏
        this.collapseInvestigation(messageGroup);
        
        // 显示最终答案
        const finalAnswer = this.extractFinalAnswer();
        messageText.innerHTML = this.formatMessage(finalAnswer);
    }

    extractFinalAnswer() {
        // 提取提问或回答问题工具的内容
        if (this.fullContent.includes('<提问>')) {
            const questionMatch = this.fullContent.match(/<question>(.*?)<\/question>/s);
            return questionMatch ? questionMatch[1].trim() : '提问内容';
        }
        
        if (this.fullContent.includes('<回答问题>')) {
            const answerMatch = this.fullContent.match(/<answer>(.*?)<\/answer>/s);
            return answerMatch ? answerMatch[1].trim() : '回答内容';
        }
        
        return this.fullContent;
    }

    collapseInvestigation(messageGroup) {
        const investigationBox = messageGroup.querySelector('.investigation-box');
        const collapsedInvestigation = messageGroup.querySelector('.collapsed-investigation');
        
        investigationBox.style.display = 'none';
        collapsedInvestigation.style.display = 'flex';
        
        // 添加点击展开功能
        collapsedInvestigation.onclick = () => {
            if (investigationBox.style.display === 'none') {
                investigationBox.style.display = 'block';
                collapsedInvestigation.style.display = 'none';
            } else {
                investigationBox.style.display = 'none';
                collapsedInvestigation.style.display = 'flex';
            }
        };
    }

    handleRoundMarker(messageGroup, roundNumber) {
        console.log(`🔄 处理调查轮次标记: round ${roundNumber}`);

        // 初始化轮次跟踪
        if (!this.investigationRounds) {
            this.investigationRounds = [];
        }

        // 保存上一轮的内容（如果存在）
        if (this.currentRound && this.currentRound !== roundNumber) {
            const previousRoundContent = this.getCurrentInvestigationContent();
            if (previousRoundContent) {
                this.saveRoundContent(this.currentRound, previousRoundContent);
            }
        }

        // 记录当前轮次并重置内容起始位置
        this.currentRound = roundNumber;
        this.roundStartPosition = this.fullContent.length; // 记录当前轮次开始的位置

        // 显示调查栏并更新轮次显示
        this.showInvestigationWithRound(messageGroup, roundNumber);
    }

    handleInvestigationFinished(messageGroup) {
        console.log('🏁 处理调查完成标记');

        // 保存当前轮次的调查内容（最后一次保存）
        if (this.currentRound) {
            const investigationContent = this.getCurrentInvestigationContent();
            if (investigationContent) {
                this.saveRoundContent(this.currentRound, investigationContent);
            }
        }

        // 折叠调查栏
        this.collapseInvestigation(messageGroup, '调查完成');

        // 标记进入最终答案阶段，让后续的流式内容显示在message-text中
        this.isShowingFinalAnswer = true;

        // 清空message-text准备显示最终答案
        const messageText = messageGroup.querySelector('.message-text');
        if (messageText) {
            messageText.innerHTML = '';
        }
    }

    getCurrentInvestigationContent() {
        // 提取当前轮次的调查内容（thinking + 工具总结）
        return this.getCurrentRoundContent();
    }



    showInvestigationWithRound(messageGroup, roundNumber) {
        const investigationBox = messageGroup.querySelector('.investigation-box');
        const investigationText = messageGroup.querySelector('.investigation-text');

        if (investigationBox && investigationText) {
            investigationBox.style.display = 'block';

            // 创建轮次徽章HTML，包括历史轮次
            const roundBadgesHtml = this.createRoundBadgesHtml(roundNumber);
            investigationText.innerHTML = `正在调查 ${roundBadgesHtml}`;

            // 绑定轮次点击事件
            this.bindRoundClickEvents(messageGroup);

            console.log(`📋 显示调查栏，轮次: ${roundNumber}`);
        }
    }

    createRoundBadgesHtml(currentRound) {
        let badgesHtml = '';

        // 创建历史轮次徽章（可点击）
        for (let i = 1; i < currentRound; i++) {
            badgesHtml += `<span class="round-badge clickable" data-session="${this.currentSessionIndex}" data-round="${i}">${i}</span>`;
        }

        // 创建当前轮次徽章（高亮显示）
        badgesHtml += `<span class="round-badge current" data-session="${this.currentSessionIndex}" data-round="${currentRound}">${currentRound}</span>`;

        return badgesHtml;
    }

    bindRoundClickEvents(messageGroup) {
        const roundBadges = messageGroup.querySelectorAll('.round-badge.clickable');

        roundBadges.forEach(badge => {
            badge.addEventListener('click', (e) => {
                const sessionIndex = parseInt(e.target.dataset.session);
                const roundNumber = parseInt(e.target.dataset.round);
                this.showHistoricalRound(messageGroup, sessionIndex, roundNumber);
            });
        });
    }

    showHistoricalRound(messageGroup, sessionIndex, roundNumber) {
        console.log(`📖 显示历史轮次: 会话${sessionIndex} 第${roundNumber}轮`);

        const investigationContent = messageGroup.querySelector('.investigation-content');
        if (!investigationContent) return;

        // 获取历史轮次内容
        const historicalContent = this.getHistoricalRoundContent(sessionIndex, roundNumber);
        if (historicalContent) {
            // 添加会话和轮次信息头部
            const sessionInfo = sessionIndex === this.currentSessionIndex ?
                `**当前会话 第${roundNumber}轮调查**\n\n---\n\n` :
                `**会话${sessionIndex} 第${roundNumber}轮调查**\n\n---\n\n`;

            const contentWithHeader = sessionInfo + historicalContent;
            investigationContent.innerHTML = this.formatMessage(contentWithHeader);

            // 更新轮次徽章状态
            this.updateRoundBadgeStates(messageGroup, sessionIndex, roundNumber);
        }
    }

    getHistoricalRoundContent(sessionIndex, roundNumber) {
        if (!this.allInvestigationRounds ||
            !this.allInvestigationRounds[sessionIndex] ||
            !this.allInvestigationRounds[sessionIndex][roundNumber - 1]) {
            return `会话${sessionIndex} 第${roundNumber}轮调查内容暂不可用`;
        }

        const roundData = this.allInvestigationRounds[sessionIndex][roundNumber - 1];
        return roundData.content;
    }

    updateRoundBadgeStates(messageGroup, selectedSessionIndex, selectedRound) {
        const roundBadges = messageGroup.querySelectorAll('.round-badge');

        roundBadges.forEach(badge => {
            const badgeSessionIndex = parseInt(badge.dataset.session);
            const badgeRoundNumber = parseInt(badge.dataset.round);
            badge.classList.remove('current', 'selected');

            // 只有同一会话的徽章才能被选中
            if (badgeSessionIndex === selectedSessionIndex && badgeRoundNumber === selectedRound) {
                badge.classList.add('selected');
            } else if (badgeSessionIndex === this.currentSessionIndex && badgeRoundNumber === this.currentRound) {
                badge.classList.add('current');
            }
        });
    }

    saveRoundContent(roundNumber, content) {
        if (!this.allInvestigationRounds) {
            this.allInvestigationRounds = [];
        }

        // 确保当前会话存在
        if (!this.allInvestigationRounds[this.currentSessionIndex]) {
            this.allInvestigationRounds[this.currentSessionIndex] = [];
        }

        // 保存轮次内容到当前会话的轮次数组中
        this.allInvestigationRounds[this.currentSessionIndex][roundNumber - 1] = {
            round: roundNumber,
            content: content,
            timestamp: Date.now(),
            sessionIndex: this.currentSessionIndex
        };

        console.log(`💾 保存会话 ${this.currentSessionIndex} 第 ${roundNumber} 轮调查内容，长度: ${content.length}`);
    }

    finalizeMessage(messageGroup) {
        // 最终处理消息
        const investigationBox = messageGroup.querySelector('.investigation-box');
        const thinkingDots = messageGroup.querySelector('.thinking-dots');

        if (investigationBox.style.display === 'block' && thinkingDots) {
            thinkingDots.style.display = 'none';
        }
        
        // 保存助手消息到当前会话
        const finalContent = this.extractFinalToolContentFromStream();
        if (finalContent) {
            this.saveMessageToSession('assistant', finalContent);
        }
    }

    // 保存消息到当前会话
    saveMessageToSession(role, content) {
        if (!this.currentSessionId) return;
        
        // 查找当前会话
        const session = this.sessions.find(s => s.id === this.currentSessionId);
        if (!session) return;
        
        // 添加消息到会话
        session.messages.push({
            role: role,
            content: content,
            timestamp: new Date().toISOString()
        });
        
        // 更新会话标题（使用第一条用户消息作为标题）
        if (role === 'user' && session.messages.length === 1) {
            // 截取前20个字符作为标题
            session.title = content.length > 20 ? content.substring(0, 20) + '...' : content;
        }
        
        // 更新时间戳
        session.updatedAt = new Date().toISOString();
        
        // 保存会话
        this.saveSessions();
        
        // 更新会话列表显示
        this.updateSessionList();
    }

    formatMessage(text) {
        if (!text) return '';
        return this.renderMarkdown(text);
    }

    renderMarkdown(content) {
        // 简单的 Markdown 渲染器
        let html = this.escapeHtml(content);

        // 代码块 (```)
        html = html.replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code class="language-$1">$2</code></pre>');

        // 行内代码 (`)
        html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

        // 标题
        html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
        html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
        html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');

        // 粗体和斜体
        html = html.replace(/\*\*\*(.*?)\*\*\*/g, '<strong><em>$1</em></strong>');
        html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

        // 链接
        html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');

        // 列表项
        html = html.replace(/^[\s]*[-*+] (.+)$/gm, '<li>$1</li>');

        // 有序列表项
        html = html.replace(/^[\s]*\d+\. (.+)$/gm, '<li class="ordered">$1</li>');

        // 包装列表
        html = html.replace(/(<li>.*?<\/li>)/gs, function(match) {
            if (match.includes('class="ordered"')) {
                return '<ol>' + match.replace(/class="ordered"/g, '') + '</ol>';
            } else {
                return '<ul>' + match + '</ul>';
            }
        });

        // 引用
        html = html.replace(/^> (.+)$/gm, '<blockquote>$1</blockquote>');

        // 分割线
        html = html.replace(/^---$/gm, '<hr>');

        // 换行
        html = html.replace(/\n/g, '<br>');

        return html;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 调试方法：显示所有历史会话的轮次信息
    showAllConversationHistory() {
        if (!this.allInvestigationRounds || this.allInvestigationRounds.length === 0) {
            console.log('没有历史会话记录');
            return;
        }

        console.log('=== 所有会话历史 (二维数组结构) ===');
        console.log(`总会话数: ${this.allInvestigationRounds.length}`);

        this.allInvestigationRounds.forEach((session, sessionIndex) => {
            console.log(`\n会话 ${sessionIndex}: ${session.length} 轮调查`);
            session.forEach((round, roundIndex) => {
                if (round) {
                    console.log(`  第 ${roundIndex + 1} 轮: ${round.content.substring(0, 50)}...`);
                }
            });
        });

        console.log(`\n当前会话索引: ${this.currentSessionIndex}`);
        console.log(`当前轮次: ${this.currentRound}`);
    }

    // 获取特定会话的特定轮次内容
    getSessionRoundContent(sessionIndex, roundNumber) {
        if (!this.allInvestigationRounds ||
            !this.allInvestigationRounds[sessionIndex] ||
            !this.allInvestigationRounds[sessionIndex][roundNumber - 1]) {
            return null;
        }

        return this.allInvestigationRounds[sessionIndex][roundNumber - 1];
    }

    // 在当前消息组中显示指定会话的指定轮次（调试用）
    showSpecificSessionRound(sessionIndex, roundNumber) {
        // 找到最新的消息组
        const messageGroups = document.querySelectorAll('.message-group.assistant');
        const latestMessageGroup = messageGroups[messageGroups.length - 1];

        if (latestMessageGroup) {
            this.showHistoricalRound(latestMessageGroup, sessionIndex, roundNumber);
            console.log(`🔍 已显示会话${sessionIndex} 第${roundNumber}轮内容`);
        } else {
            console.log('❌ 没有找到可用的消息组');
        }
    }

    // 获取会话总数
    getSessionCount() {
        return this.allInvestigationRounds ? this.allInvestigationRounds.length : 0;
    }

    // 获取特定会话的轮次总数
    getSessionRoundCount(sessionIndex) {
        if (!this.allInvestigationRounds || !this.allInvestigationRounds[sessionIndex]) {
            return 0;
        }
        return this.allInvestigationRounds[sessionIndex].length;
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new ChatApp();
});
