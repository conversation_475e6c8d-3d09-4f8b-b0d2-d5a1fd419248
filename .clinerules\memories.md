# Token Management
- 用户指出token数计算方法仍未修改，中文文本应按一字一token进行计算和估计，需要修改相关逻辑以准确反映中文字符的token消耗。

# Error Handling
- 对于400错误重试时，应跳过manageContext中的currentTokens <= maxTokens检查，直接执行消息压缩逻辑。
- 用户反馈在执行full_index_search工具后出现'判断相关性时出错'及'400 Bad Request'错误，表明工具调用后的结果处理逻辑存在问题。

# Debugging
- 在chat-agent.js的executeTool中，为所有工具（包括terminal）添加详细日志，记录执行结果、错误类型和测试状态，特别关注测试不成功的场景
- 在全文搜索的调试信息中增加每条结果的关键词命中数量显示，以便于分析和优化搜索排序逻辑。
- 基于新的CNFullText实现重新实现@/check_file_encoding.js，以解决文件路径和编码配置问题
- 整合多个关于文件编码检查失败的反馈，集中指向@/check_file_encoding.js的路径解析与编码配置问题，需优先审查该文件实现逻辑。

# Architecture
- 全文检索机制改为按documentPath子目录生成独立full_index.db，需配置fullIndexDbPath作为统一父目录存放所有子目录数据库，并确保config.json正确设置该路径
- 用户指出应通过程序查看documentPath目录中的文件，强调了documentPath在实际操作中的重要性。

# Bug Report
- chat-agent.js出现严重功能回归，所有功能失效，可能与最近的executeTool日志修改或全文检索机制变更相关
- terminal工具存在普遍性功能异常，用户明确要求修复该问题
- Android端强管控功能不生效，且全文检索未命中'天擎_faq.txt.003'中的相关字符串，需检查中文编码一致性。
- 用户反馈无法找到faq 003文件，可能与中文编码或路径配置问题有关，需要检查检索机制的文件命中准确性。
- 执行出错: jieba.cut is not a function，可能与nodejieba中文分词实现相关。
- 解压后长度不匹配问题（期望693565, 实际583526），并询问@/chat-agent.js相关错误信息的具体含义。

# Full-Text Search
- 用户提供了新的CNFullText全文检索实现，支持中文分词和最小窗口距离排序，并要求基于此重新实现文件编码检查功能
- 在'终端安全'路径下搜索时，'Android 管控'必须被明确拆分为'Android'和'管控'两个独立关键词进行匹配，以确保结果准确性和相关性。
- 用户要求使用 better-sqlite3 和 sqlite-fts5 重构 full_index_search 实现以提升全文检索性能。
- 全文搜索应按关键词距离排序结果，并确保命中所有查询关键词才能返回结果。
- 用户询问关键词'强管控'的分词处理方式，关注中文分词在全文检索中的具体实现逻辑。
- 用户关注当前系统是否支持fts5功能，用于提升全文检索性能。
- 新的CNFullText实现使用nodejieba进行中文分词，构建带位置信息的倒排索引，并通过最小窗口距离进行结果排序

# Testing
- 需明确测试'终端安全'路径下'Android 管控'作为两个独立关键词的搜索场景，确保测试覆盖该类多关键词匹配逻辑。

# Code Logic Optimization
- 用户质疑chat-agent.js中对中文、中英文混合和非中文关键词的处理逻辑重复，建议审查并优化该部分代码以避免冗余。
