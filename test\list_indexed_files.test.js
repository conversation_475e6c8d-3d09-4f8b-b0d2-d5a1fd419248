const sqlite3 = require('sqlite3').verbose();
const path = require('path');

async function listIndexedFiles() {
    const dbPath = path.join(__dirname, '../full_index.db');
    const db = new sqlite3.Database(dbPath);

    try {
        // 查询所有文件并按路径分组
        const files = await new Promise((resolve, reject) => {
            const files = [];
            db.each(
                `SELECT file_path, file_name 
                 FROM documents 
                 ORDER BY file_path`,
                (err, row) => {
                    if (err) reject(err);
                    else files.push(row);
                },
                (err) => {
                    if (err) reject(err);
                    else resolve(files);
                }
            );
        });

        console.log(`数据库中索引了 ${files.length} 个文件:`);
        console.log('----------------------------------------');

        // 按目录分组显示
        const dirMap = {};
        files.forEach(file => {
            const dir = path.dirname(file.file_path);
            if (!dirMap[dir]) {
                dirMap[dir] = [];
            }
            dirMap[dir].push(path.basename(file.file_path));
        });

        // 显示前20个目录的文件列表
        Object.entries(dirMap).slice(0, 20).forEach(([dir, files]) => {
            console.log(`目录: ${dir}`);
            files.forEach(file => {
                console.log(`  - ${file}`);
            });
            console.log('');
        });

        if (Object.keys(dirMap).length > 20) {
            console.log(`...还有 ${Object.keys(dirMap).length - 20} 个目录未显示`);
        }

    } catch (error) {
        console.error('查询出错:', error);
    } finally {
        db.close();
    }
}

listIndexedFiles();
