const XLSX = require('xlsx');
const axios = require('axios');
const fs = require('fs').promises;
const config = require('./config.json');

// 从config.json获取API配置
const API_CONFIG = {
    baseUrl: config.api.baseUrl,
    modelId: config.api.modelId,
    apiKey: config.api.apiKey
};

// 并发控制参数
const CONCURRENT_LIMIT = 3;
const MIN_DELAY = 100;
const MAX_DELAY = 300;

/**
 * 读取Excel文件
 * @param {string} filePath - Excel文件路径
 * @returns {Array} 包含问题和答案的对象数组
 */
function readExcelFile(filePath) {
    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0]; // 假设数据在第一个工作表
    const worksheet = workbook.Sheets[sheetName];
    
    // 将工作表转换为JSON
    const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    
    // 假设第一行是标题行，包含"问题"和"答案"
    const headers = data[0];
    const questionIndex = headers.findIndex(header => header.includes('问题'));
    const answerIndex = headers.findIndex(header => header.includes('答案'));
    
    // 处理数据行
    const faqData = [];
    for (let i = 1; i < data.length; i++) {
        const row = data[i];
        if (row[questionIndex] && row[answerIndex]) {
            faqData.push({
                question: row[questionIndex],
                answer: row[answerIndex]
            });
        }
    }
    
    return faqData;
}

/**
 * 调用API对问题进行分类
 * @param {string} question - 问题文本
 * @returns {Promise<Array>} 产品类目数组
 */
async function classifyQuestion(question) {
    const prompt = `请根据以下问题判断它属于哪个产品类目：
        类目：天擎
        描述：奇安信的终端安全产品，包括终端管控，病毒查杀，分析，防护有关的功能
        类目：天眼
        描述：奇安信的流量分析，监控，威胁发现产品
        类目：椒图
        描述：奇安信的服务器安全产品，包括服务器的漏洞管理，基线检查，防护等功能
        类目：ngsoc
        描述：奇安信的网络安全运营管理系统，包括安全事件管理，安全运营，事件调查处置等有关的功能
        类目：防火墙
        描述：奇安信的网络边界安全产品，负责网络安全边界防护，隔离，管控
        类目：云安全
        描述：奇安信的云计算安全解决方案，包括云计算安全，虚拟化安全，网站云安全，云安全服务平台等
        类目：工业安全
        描述：奇安信的工业控制系统安全产品，包括工控安全防护，安全检测审计，工控安全运营管理，工控态势感知等
        尽量归纳到以上分类中，确实不在上述分类中的，可以归类为"其他"。
        注意：只返回产品分类名称，可以有多个分类，用逗号分隔。\n问题: ${question}`;
    
    try {
        const response = await axios.post(
            `${API_CONFIG.baseUrl}/chat/completions`,
            {
                model: API_CONFIG.modelId,
                messages: [
                    { role: "user", content: prompt }
                ],
                temperature: 0.1,
                max_tokens: 40
            },
            {
                headers: {
                    'Authorization': `Bearer ${API_CONFIG.apiKey}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        // 解析返回的分类结果
        const categories = response.data.choices[0].message.content.trim();
        // 按逗号分割分类结果
        const productNames = categories.split(',').map(cat => cat.trim()).filter(cat => cat.length > 0);
        
        return productNames.length > 0 ? productNames : ['其他'];
    } catch (error) {
        console.error('API调用失败:', error.message);
        return ['其他']; // 默认分类
    }
}

/**
 * 随机延迟函数
 * @param {number} min - 最小延迟时间（毫秒）
 * @param {number} max - 最大延迟时间（毫秒）
 * @returns {Promise<void>}
 */
function randomDelay(min, max) {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
    return new Promise(resolve => setTimeout(resolve, delay));
}

/**
 * 限制并发数量的函数
 * @param {Array} tasks - 任务数组
 * @param {number} limit - 并发限制
 * @returns {Promise<Array>}
 */
async function concurrentLimit(tasks, limit) {
    const results = [];
    
    for (let i = 0; i < tasks.length; i += limit) {
        const batch = tasks.slice(i, i + limit);
        const batchPromises = batch.map(async (task, index) => {
            const result = await task();
            await randomDelay(MIN_DELAY, MAX_DELAY);
            return result;
        });
        
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
    }
    
    return results;
}

/**
 * 主函数
 */
async function main() {
    try {
        // 读取Excel文件
        const excelFilePath = './test_docs/20250726_000000_000.xlsx';
        console.log('正在读取Excel文件...');
        const faqData = readExcelFile(excelFilePath);
        console.log(`共读取到 ${faqData.length} 条FAQ数据`);
        
        // 一边分类一边写文件
        console.log('开始对问题进行分类并写入文件...');
        
        // 用于存储每个分类的FAQ数据
        const categoryData = {};
        
        // 为每个问题创建分类任务
        const tasks = faqData.map((item, index) => {
            return async () => {
                console.log(`正在处理第 ${index + 1} 个问题...`);
                const categories = await classifyQuestion(item.question);
                console.log(`第 ${index + 1} 个问题分类为: ${categories.join(', ')}`);
                
                // 为每个分类写入FAQ数据
                for (const category of categories) {
                    // 初始化分类数据
                    if (!categoryData[category]) {
                        categoryData[category] = [];
                    }
                    
                    // 添加问题到对应分类
                    categoryData[category].push(item);
                    
                    // 立即写入文件
                    const fileName = `${category}_faq.txt`;
                    const content = `问题:${item.question}\n答案:${item.answer}\n\n`;
                    // 修复可能的格式问题
                    const fixedContent = content.replace(/^:问题:/, '问题:');
                    await fs.appendFile(fileName, fixedContent, 'utf8');
                }
                
                return {
                    ...item,
                    categories
                };
            };
        });
        
        // 并发执行分类任务
        const classifiedData = await concurrentLimit(tasks, CONCURRENT_LIMIT);
        
        console.log('处理完成！');
    } catch (error) {
        console.error('处理过程中出现错误:', error);
    }
}

// 执行主函数
main();
