#!/bin/bash

# Web聊天界面启动脚本

echo "=== Web聊天界面启动脚本 ==="
echo ""

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "错误: 未找到Node.js，请先安装Node.js"
    exit 1
fi

echo "Node.js版本: $(node --version)"

# 检查必要文件是否存在
if [ ! -f "config.json" ]; then
    echo "错误: 未找到config.json配置文件"
    exit 1
fi

if [ ! -f "system-prompt.txt" ]; then
    echo "错误: 未找到system-prompt.txt系统提示词文件"
    exit 1
fi

if [ ! -f "chat_agent_server.js" ]; then
    echo "错误: 未找到chat_agent_server.js服务器文件"
    exit 1
fi

# 检查依赖是否安装
if [ ! -d "node_modules" ]; then
    echo "正在安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "错误: 依赖安装失败"
        exit 1
    fi
fi

echo ""
echo "启动Web聊天服务器..."
echo "服务器地址: http://localhost:3000"
echo "按 Ctrl+C 停止服务器"
echo ""

# 启动服务器
node chat_agent_server.js
