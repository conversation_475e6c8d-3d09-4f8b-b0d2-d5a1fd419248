* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    background: #f7f7f8;
    height: 100vh;
    overflow: hidden;
}

.app {
    display: flex;
    height: 100vh;
}

/* 侧边栏 */
.sidebar {
    width: 260px;
    background: #171717;
    color: white;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #2d2d30;
}

.sidebar-header {
    padding: 16px;
    border-bottom: 1px solid #2d2d30;
}

.new-chat-btn {
    width: 100%;
    background: transparent;
    border: 1px solid #4d4d4f;
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.new-chat-btn:hover {
    background: #2d2d30;
}

.chat-history {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
}

.session-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 8px;
    border-radius: 8px;
    margin-bottom: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    color: #e5e5e5;
}

.session-item:hover {
    background: #2d2d30;
}

.session-item .session-title {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 8px;
}

.session-item .session-actions {
    display: none;
}

.session-item:hover .session-actions {
    display: flex;
}

.session-item .delete-btn {
    background: transparent;
    border: none;
    color: #9ca3af;
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.session-item .delete-btn:hover {
    background: #4d4d4f;
    color: #ffffff;
}

/* 主聊天区域 */
.main-chat {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
}

/* 头部 */
.chat-header {
    background: white;
    border-bottom: 1px solid #e5e5e5;
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-title h1 {
    font-size: 18px;
    font-weight: 600;
    color: #1f1f1f;
    margin-bottom: 2px;
}

.status-indicator {
    font-size: 12px;
    color: #10a37f;
    font-weight: 500;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    background: transparent;
    border: none;
    color: #6e6e80;
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: #f5f5f5;
    color: #1f1f1f;
}

/* 消息容器 */
.messages-container {
    flex: 1;
    overflow-y: auto;
    background: white;
}

.messages {
    max-width: 768px;
    margin: 0 auto;
    padding: 24px;
}

/* 消息组 */
.message-group {
    display: flex;
    margin-bottom: 24px;
    gap: 16px;
}

.message-group.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    flex-shrink: 0;
    margin-top: 4px;
}

.avatar-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #10a37f;
    color: white;
}

.message-group.user .avatar-circle {
    background: #6366f1;
}

.message-content {
    flex: 1;
    min-width: 0;
}

.message-text {
    color: #1f1f1f;
    line-height: 1.6;
    font-size: 16px;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.message-group.user .message-text {
    background: #f4f4f4;
    padding: 12px 16px;
    border-radius: 18px;
    border-bottom-right-radius: 4px;
    display: inline-block;
    max-width: fit-content;
    margin-left: auto;
}

/* 调查栏样式 */
.investigation-box {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
}

.investigation-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #f1f3f4;
    border-bottom: 1px solid #e9ecef;
    font-size: 14px;
    font-weight: 500;
    color: #5f6368;
    cursor: pointer;
}

.investigation-toggle {
    margin-left: auto;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.investigation-toggle:hover {
    background-color: #e8eaed;
}

.investigation-icon {
    display: flex;
    align-items: center;
}

.thinking-dots {
    display: flex;
    gap: 2px;
}

.thinking-dots span {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #10a37f;
    animation: thinkingPulse 1.4s ease-in-out infinite both;
}

.thinking-dots span:nth-child(1) { animation-delay: -0.32s; }
.thinking-dots span:nth-child(2) { animation-delay: -0.16s; }
.thinking-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes thinkingPulse {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 轮次徽章样式 */
.round-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    height: 20px;
    background: #10a37f;
    color: white;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 600;
    margin-left: 8px;
    padding: 0 6px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.round-badge:hover {
    background: #0d8f6b;
}

.round-badge.clickable {
    background: #6b7280;
}

.round-badge.clickable:hover {
    background: #4b5563;
}

.round-badge.current {
    background: #10a37f;
    animation: pulse 2s infinite;
}

.round-badge.selected {
    background: #3b82f6;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Markdown 渲染样式 */
.message-text h1, .investigation-content h1 {
    font-size: 1.5em;
    font-weight: bold;
    margin: 16px 0 8px 0;
    color: #1a1a1a;
}

.message-text h2, .investigation-content h2 {
    font-size: 1.3em;
    font-weight: bold;
    margin: 14px 0 6px 0;
    color: #1a1a1a;
}

.message-text h3, .investigation-content h3 {
    font-size: 1.1em;
    font-weight: bold;
    margin: 12px 0 4px 0;
    color: #1a1a1a;
}

.message-text code, .investigation-content code {
    background: #f4f4f4;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    color: #d73a49;
}

.message-text pre, .investigation-content pre {
    background: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    padding: 16px;
    overflow-x: auto;
    margin: 12px 0;
}

.message-text pre code, .investigation-content pre code {
    background: none;
    padding: 0;
    color: #24292e;
    font-size: 0.85em;
}

.message-text ul, .investigation-content ul {
    margin: 8px 0;
    padding-left: 20px;
}

.message-text ol, .investigation-content ol {
    margin: 8px 0;
    padding-left: 20px;
}

.message-text li, .investigation-content li {
    margin: 4px 0;
}

.message-text blockquote, .investigation-content blockquote {
    border-left: 4px solid #dfe2e5;
    padding-left: 16px;
    margin: 12px 0;
    color: #6a737d;
    font-style: italic;
}

.message-text hr, .investigation-content hr {
    border: none;
    border-top: 1px solid #e1e4e8;
    margin: 16px 0;
}

.message-text a, .investigation-content a {
    color: #0366d6;
    text-decoration: none;
}

.message-text a:hover, .investigation-content a:hover {
    text-decoration: underline;
}

.message-text strong, .investigation-content strong {
    font-weight: 600;
}

.message-text em, .investigation-content em {
    font-style: italic;
}



.investigation-content {
    padding: 12px 16px;
    font-size: 14px;
    line-height: 1.5;
    color: #3c4043;
    white-space: pre-wrap;
}

/* 折叠的调查栏 */
.collapsed-investigation {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 13px;
    color: #5f6368;
}

.collapsed-investigation:hover {
    background: #f1f3f4;
}

.collapsed-header {
    display: flex;
    align-items: center;
    gap: 6px;
}

.collapsed-header svg {
    transition: transform 0.2s ease;
}

.collapsed-investigation.expanded .collapsed-header svg {
    transform: rotate(90deg);
}

/* 流式显示效果 */
.typing-indicator {
    display: inline-block;
    width: 8px;
    height: 20px;
    background: #10a37f;
    animation: typing 1s infinite;
    margin-left: 2px;
}

@keyframes typing {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* 输入区域 */
.input-container {
    background: white;
    border-top: 1px solid #e5e5e5;
    padding: 24px;
}

.input-wrapper {
    max-width: 768px;
    margin: 0 auto;
}

.input-box {
    display: flex;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 12px;
    padding: 12px 16px;
    gap: 12px;
    align-items: flex-end;
    transition: all 0.2s ease;
    box-shadow: 0 0 0 0 rgba(16, 163, 127, 0);
}

.input-box:focus-within {
    border-color: #10a37f;
    box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.1);
}

#messageInput {
    flex: 1;
    border: none;
    outline: none;
    resize: none;
    font-family: inherit;
    font-size: 16px;
    line-height: 1.5;
    background: transparent;
    max-height: 200px;
    min-height: 24px;
}

#messageInput::placeholder {
    color: #9ca3af;
}

.send-button {
    background: #10a37f;
    border: none;
    color: white;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 36px;
}

.send-button:hover:not(:disabled) {
    background: #0d8f6b;
}

.send-button:disabled {
    background: #d1d5db;
    cursor: not-allowed;
}

.input-footer {
    margin-top: 8px;
    text-align: center;
}

.input-hint {
    font-size: 12px;
    color: #6b7280;
}

/* 加载状态 */
.loading-message {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    font-style: italic;
}

.loading-dots {
    display: flex;
    gap: 2px;
}

.loading-dots span {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #10a37f;
    animation: loadingPulse 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes loadingPulse {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 滚动条样式 */
.messages-container::-webkit-scrollbar,
.chat-history::-webkit-scrollbar {
    width: 6px;
}

.messages-container::-webkit-scrollbar-track,
.chat-history::-webkit-scrollbar-track {
    background: transparent;
}

.messages-container::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

.chat-history::-webkit-scrollbar-thumb {
    background: #4d4d4f;
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

.chat-history::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        display: none;
    }
    
    .messages {
        padding: 16px;
    }
    
    .input-container {
        padding: 16px;
    }
    
    .chat-header {
        padding: 12px 16px;
    }
    
    #messageInput {
        font-size: 16px; /* 防止iOS缩放 */
    }
}
