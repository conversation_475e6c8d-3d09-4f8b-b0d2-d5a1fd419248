// 测试分词效果
const { CNFullText } = require('./indexer.js');

// 创建索引实例
const engine = new CNFullText();

// 测试文本
const testTexts = [
  "这是一个简单的测试文本",
  "中华人民共和国成立于1949年",
  "JavaScript是一种编程语言",
  "人工智能技术发展迅速",
  "北京大学计算机科学与技术专业",
  "深度学习神经网络算法优化",
  "超长词汇测试：中华人民共和国国家发展和改革委员会经济体制综合改革司",
  "混合文本：OpenAI的GPT模型在自然语言处理领域取得了重大突破",
  "人工智能和机器学习技术在现代社会中发挥重要作用",
  "天擎的外设库，离线工具可以进行更新吗?"
];

console.log("=== 分词测试 ===");
testTexts.forEach((text, index) => {
  console.log(`\n文本 ${index + 1}: ${text}`);
  const tokens = engine._tokenize(text);
  console.log(`分词结果 (${tokens.length} 个): ${tokens.join(' | ')}`);
});

// 测试建索引和搜索
console.log("\n=== 索引和搜索测试 ===");
const docs = testTexts.map((text, index) => ({
  id: index + 1,
  text: text
}));

engine.build(docs);

// 测试搜索
const queries = ["技术", "大学", "中华", "JavaScript"];
queries.forEach(query => {
  console.log(`\n搜索: "${query}"`);
  const results = engine.search(query, { topK: 3 });
  results.forEach((result, index) => {
    console.log(`  ${index + 1}. [ID:${result.id}] 覆盖率:${result.coverage.toFixed(2)} 距离:${result.proximity}`);
    console.log(`     ${result.doc.text}`);
    if (result.snippets.length > 0) {
      console.log(`     片段: ${result.snippets[0].snippet}`);
    }
  });
});

// 测试距离过滤功能
console.log("\n=== 距离过滤测试 ===");
const testQuery = "人工 技术";
console.log(`\n测试查询: "${testQuery}"`);

console.log("\n不限制距离:");
const resultsNoLimit = engine.search(testQuery, { topK: 5, requireAll: false });
resultsNoLimit.forEach((result, index) => {
  console.log(`  ${index + 1}. [ID:${result.id}] 覆盖率:${result.coverage.toFixed(2)} 距离:${result.proximity}`);
  console.log(`     ${result.doc.text}`);
});

console.log("\n限制距离 <= 10:");
const resultsWithLimit = engine.search(testQuery, { topK: 5, requireAll: false, maxDistance: 10 });
resultsWithLimit.forEach((result, index) => {
  console.log(`  ${index + 1}. [ID:${result.id}] 覆盖率:${result.coverage.toFixed(2)} 距离:${result.proximity}`);
  console.log(`     ${result.doc.text}`);
});

console.log("\n限制距离 <= 5:");
const resultsStrictLimit = engine.search(testQuery, { topK: 5, requireAll: false, maxDistance: 5 });
resultsStrictLimit.forEach((result, index) => {
  console.log(`  ${index + 1}. [ID:${result.id}] 覆盖率:${result.coverage.toFixed(2)} 距离:${result.proximity}`);
  console.log(`     ${result.doc.text}`);
});

console.log("\n限制距离 <= 2:");
const resultsVeryStrictLimit = engine.search(testQuery, { topK: 5, requireAll: false, maxDistance: 2 });
resultsVeryStrictLimit.forEach((result, index) => {
  console.log(`  ${index + 1}. [ID:${result.id}] 覆盖率:${result.coverage.toFixed(2)} 距离:${result.proximity}`);
  console.log(`     ${result.doc.text}`);
});
