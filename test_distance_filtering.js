// 测试片段级别的距离过滤功能
const { CNFullText } = require('./indexer.js');

// 添加调试功能
function debugTokenize(engine, text) {
  const tokens = engine._tokenize(engine._normalize(text));
  console.log(`调试分词: "${text}" -> [${tokens.join(', ')}]`);
  return tokens;
}

// 创建测试数据
const testDocs = [
  {
    id: 'doc1',
    content: `第一行：人工智能技术发展迅速
第二行：机器学习算法不断改进
第三行：深度学习网络结构复杂
第四行：这里有人工和技术两个词很近
第五行：自然语言处理应用广泛
第六行：计算机视觉识别准确
第七行：这里人工在这里，技术在很远的地方，距离超过限制`
  },
  {
    id: 'doc2', 
    content: `第一行：网络安全防护重要
第二行：数据加密技术先进
第三行：防火墙配置复杂
第四行：入侵检测系统智能
第五行：安全审计日志详细`
  },
  {
    id: 'doc3',
    content: `第一行：人工智能和技术结合
第二行：创新应用层出不穷
第三行：未来发展前景广阔
第四行：这段没有关键词
第五行：人工智能技术革命性突破`
  }
];

console.log('=== 测试片段级别距离过滤功能 ===\n');

// 创建搜索引擎
const engine = new CNFullText({
  textField: 'content',
  idField: 'id',
  snippetWidth: 40,
  mergeGap: 3
});

// 构建索引
engine.build(testDocs);

const testQuery = "人工 技术";
console.log(`测试查询: "${testQuery}"\n`);

// 调试分词结果
console.log('调试查询分词:');
debugTokenize(engine, testQuery);
console.log('\n调试测试文档分词:');
debugTokenize(engine, '第七行：这里人工在这里，技术在很远的地方，距离超过限制');
console.log('');

// 测试1：不限制距离
console.log('1. 不限制距离:');
const resultsNoLimit = engine.search(testQuery, { 
  topK: 10, 
  requireAll: false,
  maxDistance: null
});

resultsNoLimit.forEach((result, index) => {
  console.log(`  ${index + 1}. [ID:${result.id}] 覆盖率:${result.coverage.toFixed(2)} 距离:${result.proximity}`);
  result.snippets.forEach((snippet, snippetIndex) => {
    console.log(`     片段${snippetIndex + 1} (第${snippet.lines[0]}-${snippet.lines[1]}行):`);
    console.log(`     ${snippet.snippet}`);
  });
  console.log('');
});

// 测试2：限制距离 <= 5
console.log('2. 限制距离 <= 5:');
const resultsWithLimit = engine.search(testQuery, { 
  topK: 10, 
  requireAll: false,
  maxDistance: 5
});

resultsWithLimit.forEach((result, index) => {
  console.log(`  ${index + 1}. [ID:${result.id}] 覆盖率:${result.coverage.toFixed(2)} 距离:${result.proximity}`);
  result.snippets.forEach((snippet, snippetIndex) => {
    console.log(`     片段${snippetIndex + 1} (第${snippet.lines[0]}-${snippet.lines[1]}行):`);
    console.log(`     ${snippet.snippet}`);
  });
  console.log('');
});

// 测试3：限制距离 <= 2
console.log('3. 限制距离 <= 2:');
const resultsStrictLimit = engine.search(testQuery, { 
  topK: 10, 
  requireAll: false,
  maxDistance: 2
});

resultsStrictLimit.forEach((result, index) => {
  console.log(`  ${index + 1}. [ID:${result.id}] 覆盖率:${result.coverage.toFixed(2)} 距离:${result.proximity}`);
  result.snippets.forEach((snippet, snippetIndex) => {
    console.log(`     片段${snippetIndex + 1} (第${snippet.lines[0]}-${snippet.lines[1]}行):`);
    console.log(`     ${snippet.snippet}`);
  });
  console.log('');
});

console.log('=== 测试完成 ===');
