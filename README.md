# Excel FAQ 处理工具

这个工具用于处理包含问题和答案的Excel文件，根据产品类目对问题进行分类，并将同一类别的问题和答案保存到相应的文件中。

## 功能特性

1. 读取Excel文件中的问题和答案对
2. 使用配置的模型API按产品类目对问题进行分类
3. 支持一个问题属于多个分类
4. 一边分类一边写入文件
5. 并发处理（3个并发请求）
6. 每次请求后随机暂停100ms～300ms
7. 不过滤分类结果，分什么类就记什么

## 安装依赖

```bash
npm install
```

## 配置

在 `config.json` 文件中配置API信息：

```json
{
  "api": {
    "baseUrl": "https://aip.b.qianxin-inc.cn/v2",
    "modelId": "DeepSeek-V3-0324",
    "apiKey": "your-api-key"
  }
}
```

## 使用方法

```bash
# 处理完整的Excel文件
node process-excel-faq.js
```

## 输出文件

处理完成后，会生成以下格式的文件：
- `产品名_faq.txt` - 包含该产品类别的所有问题和答案

文件格式：
```
问题:问题内容
答案:答案内容

问题:问题内容
答案:答案内容
```

## Excel文件格式

Excel文件应包含两列：
- 问题：包含问题内容的列
- 答案：包含答案内容的列

第一行应为列标题。
